package com.moneta.sampleapp

import android.app.Application
import com.moneta.sdk.MonetaSDK
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class MonetaSampleApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize Moneta SDK
        // In a real app, you would get this URL from your configuration
        val baseUrl = "https://membership-portal.dev.pressingly.net/api/" // Replace with your actual API URL
        MonetaSDK.initialize(this, baseUrl)
    }
}
