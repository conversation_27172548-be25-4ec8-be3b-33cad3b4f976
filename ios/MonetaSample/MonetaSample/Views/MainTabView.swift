import SwiftUI

struct MainTabView: View {
    var body: some View {
        TabView {
            DashboardView()
                .tabItem {
                    Image(systemName: "chart.pie.fill")
                    Text(LocalizedKeys.Navigation.dashboard.localized)
                }
                .tag(0)

            TransactionsView()
                .tabItem {
                    Image(systemName: "list.bullet.rectangle.fill")
                    Text(LocalizedKeys.Navigation.transactions.localized)
                }
                .tag(1)

            RecommendationsView()
                .tabItem {
                    Image(systemName: "star.fill")
                    Text(LocalizedKeys.Navigation.recommendations.localized)
                }
                .tag(2)

            PublisherAuthView()
                .tabItem {
                    Image(systemName: "qrcode")
                    Text(LocalizedKeys.Navigation.publisherAuth.localized)
                }
                .tag(3)

            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text(LocalizedKeys.Navigation.settings.localized)
                }
                .tag(4)
        }
        .accentColor(.blue)
    }
}

#Preview {
    MainTabView()
}
