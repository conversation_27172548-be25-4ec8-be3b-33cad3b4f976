import Foundation
import MonetaSDK

@MainActor
class DashboardViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var balance: UserBalanceResponse?
    @Published var recentTransactions: [TransactionResponse] = []
    @Published var errorMessage: String?
    
    func loadDashboardData() async {
        isLoading = true
        errorMessage = nil

        // Load recent transactions (balance functionality not available in current SDK)
        await loadRecentTransactions()

        isLoading = false
    }
    
    private func loadRecentTransactions() async {
        do {
            // Use the available getTransactions method from the SDK
            let response = try await MonetaSDK.shared.getTransactions(page: 0, size: 5)
            self.recentTransactions = response.content
        } catch {
            print("Failed to load transactions: \(error)")
            errorMessage = "Failed to load transactions: \(error.localizedDescription)"
        }
    }
    
    func clearError() {
        errorMessage = nil
    }
}
