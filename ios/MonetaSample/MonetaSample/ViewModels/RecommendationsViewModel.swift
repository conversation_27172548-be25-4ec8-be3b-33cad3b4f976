import Foundation
import MonetaSDK

@MainActor
class RecommendationsViewModel: ObservableObject {
    @Published var recommendations: [UARecommendation] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    func loadRecommendations() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Create a sample user profile for recommendations
            let userProfile = UserProfile(
                demographics: nil,
                contentPreferences: ContentPreferences(interests: ["finance", "technology", "shopping"])
            )

            let data = try await MonetaSDK.shared.getRecommendations(
                userId: "sample_user_123",
                userProfile: userProfile,
                offset: 0,
                limit: 20
            )

            // Convert AnyCodable array to UARecommendation array
            recommendations = data.compactMap { item in
                // In a real implementation, you would properly decode the AnyCodable
                // For demo purposes, we'll create sample recommendations
                return createSampleRecommendation(from: item)
            }
            
            // If no recommendations from API, show sample data
            if recommendations.isEmpty {
                recommendations = createSampleRecommendations()
            }
            
        } catch {
            errorMessage = "Failed to load recommendations: \(error.localizedDescription)"
            // Show sample data on error for demo purposes
            recommendations = createSampleRecommendations()
        }
        
        isLoading = false
    }
    
    private func createSampleRecommendation(from anyCodable: AnyCodable) -> UARecommendation {
        // In a real app, you would properly decode the AnyCodable to UARecommendation
        // For demo purposes, return a sample recommendation
        return UARecommendation(
            id: UUID().uuidString,
            title: "Sample Recommendation",
            description: "This is a sample recommendation based on your preferences",
            url: "https://example.com",
            source: "Moneta AI",
            score: 0.85
        )
    }
    
    private func createSampleRecommendations() -> [UARecommendation] {
        return [
            UARecommendation(
                id: "1",
                title: "High-Yield Savings Account",
                description: "Earn 4.5% APY with this online savings account. No minimum balance required.",
                url: "https://example.com/savings",
                source: "Financial Partner",
                score: 0.92
            ),
            UARecommendation(
                id: "2",
                title: "Cashback Credit Card",
                description: "Get 2% cashback on all purchases with no annual fee. Perfect for everyday spending.",
                url: "https://example.com/creditcard",
                source: "Credit Partner",
                score: 0.88
            ),
            UARecommendation(
                id: "3",
                title: "Investment Portfolio",
                description: "Diversified portfolio with low fees. Start investing with as little as $100.",
                url: "https://example.com/invest",
                source: "Investment Partner",
                score: 0.75
            ),
            UARecommendation(
                id: "4",
                title: "Budget Tracking App",
                description: "Track your spending and set savings goals with this award-winning app.",
                url: "https://example.com/budget",
                source: "App Store",
                score: 0.82
            )
        ]
    }
    
    func clearError() {
        errorMessage = nil
    }
}
