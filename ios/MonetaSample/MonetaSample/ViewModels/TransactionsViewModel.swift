import Foundation
import MonetaSDK

@MainActor
class TransactionsViewModel: ObservableObject {
    @Published var transactions: [TransactionResponse] = []
    @Published var isLoading = false
    @Published var isLoadingMore = false
    @Published var hasMorePages = true
    @Published var errorMessage: String?
    
    private var currentPage = 0
    private let pageSize = 20
    
    func loadTransactions(refresh: Bool = false) async {
        if refresh {
            currentPage = 0
            hasMorePages = true
            transactions.removeAll()
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let response = try await MonetaSDK.shared.getTransactions(page: currentPage, size: pageSize)

            if refresh {
                transactions = response.content
            } else {
                transactions.append(contentsOf: response.content)
            }

            hasMorePages = currentPage < response.totalPages - 1
            currentPage += 1

        } catch {
            errorMessage = "Failed to load transactions: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func loadMoreTransactions() async {
        guard hasMorePages && !isLoadingMore else { return }
        
        isLoadingMore = true
        
        do {
            let response = try await MonetaSDK.shared.getTransactions(page: currentPage, size: pageSize)

            transactions.append(contentsOf: response.content)
            hasMorePages = currentPage < response.totalPages - 1
            currentPage += 1

        } catch {
            errorMessage = "Failed to load more transactions: \(error.localizedDescription)"
        }
        
        isLoadingMore = false
    }
    
    func clearError() {
        errorMessage = nil
    }
}
