import XCTest
@testable import MonetaSDK

final class AuthenticationTests: XCTestCase {
    
    // MARK: - OnboardingRequest Tests
    
    func testOnboardingRequestEncoding() throws {
        let request = OnboardingRequest(
            deviceName: "iPhone 14 Pro",
            deviceToken: "sample-device-token-123",
            publicKey: "sample-public-key-456"
        )
        
        let encoder = JSONEncoder()
        encoder.keyEncodingStrategy = .convertToSnakeCase
        let data = try encoder.encode(request)
        let json = try JSONSerialization.jsonObject(with: data) as! [String: Any]
        
        XCTAssertEqual(json["device_name"] as? String, "iPhone 14 Pro")
        XCTAssertEqual(json["device_token"] as? String, "sample-device-token-123")
        XCTAssertEqual(json["public_key"] as? String, "sample-public-key-456")
    }
    
    func testOnboardingRequestDecoding() throws {
        let json = """
        {
            "device_name": "Pixel 7 Pro",
            "device_token": "android-device-token-789",
            "public_key": "android-public-key-abc"
        }
        """.data(using: .utf8)!
        
        let decoder = JSONDecoder()
        decoder.keyDecodingStrategy = .convertFromSnakeCase
        let request = try decoder.decode(OnboardingRequest.self, from: json)
        
        XCTAssertEqual(request.deviceName, "Pixel 7 Pro")
        XCTAssertEqual(request.deviceToken, "android-device-token-789")
        XCTAssertEqual(request.publicKey, "android-public-key-abc")
    }
    
    // MARK: - PublisherLoginRequest Tests
    
    func testPublisherLoginRequestEncoding() throws {
        let user = PublisherLoginUser(email: "<EMAIL>", password: "password123")
        let request = PublisherLoginRequest(user: user)
        
        let encoder = JSONEncoder()
        encoder.keyEncodingStrategy = .convertToSnakeCase
        let data = try encoder.encode(request)
        let json = try JSONSerialization.jsonObject(with: data) as! [String: Any]
        
        let userDict = json["user"] as! [String: Any]
        XCTAssertEqual(userDict["email"] as? String, "<EMAIL>")
        XCTAssertEqual(userDict["password"] as? String, "password123")
    }
    
    func testPublisherLoginRequestDecoding() throws {
        let json = """
        {
            "user": {
                "email": "<EMAIL>",
                "password": "different-password"
            }
        }
        """.data(using: .utf8)!
        
        let decoder = JSONDecoder()
        decoder.keyDecodingStrategy = .convertFromSnakeCase
        let request = try decoder.decode(PublisherLoginRequest.self, from: json)
        
        XCTAssertEqual(request.user.email, "<EMAIL>")
        XCTAssertEqual(request.user.password, "different-password")
    }
    
    // MARK: - Response Model Tests
    
    func testOnboardVerificationResponseDecoding() throws {
        let json = """
        {
            "request_id": "req-123",
            "expires_at": "2024-07-01T12:00:00Z",
            "device_code": "device-code-abc"
        }
        """.data(using: .utf8)!
        
        let decoder = JSONDecoder()
        decoder.keyDecodingStrategy = .convertFromSnakeCase
        let response = try decoder.decode(OnboardVerificationResponse.self, from: json)
        
        XCTAssertEqual(response.requestId, "req-123")
        XCTAssertEqual(response.expiresAt, "2024-07-01T12:00:00Z")
        XCTAssertEqual(response.deviceCode, "device-code-abc")
    }
    
    func testOnboardUserResponseDecoding() throws {
        let json = """
        {
            "device_id": "device-789",
            "device_token": "device-token-789",
            "user_id": "user-456"
        }
        """.data(using: .utf8)!
        
        let decoder = JSONDecoder()
        decoder.keyDecodingStrategy = .convertFromSnakeCase
        let response = try decoder.decode(OnboardUserResponse.self, from: json)
        
        XCTAssertEqual(response.deviceId, "device-789")
        XCTAssertEqual(response.deviceToken, "device-token-789")
        XCTAssertEqual(response.userId, "user-456")
    }
    
    func testPublisherLoginResponseDecoding() throws {
        let json = """
        {
            "id": "session-123",
            "status": "success",
            "expires_at": "2024-07-01T18:00:00Z",
            "token": "auth-token-xyz"
        }
        """.data(using: .utf8)!
        
        let decoder = JSONDecoder()
        decoder.keyDecodingStrategy = .convertFromSnakeCase
        let response = try decoder.decode(PublisherLoginResponse.self, from: json)
        
        XCTAssertEqual(response.id, "session-123")
        XCTAssertEqual(response.status, "success")
        XCTAssertEqual(response.expiresAt, "2024-07-01T18:00:00Z")
        XCTAssertEqual(response.token, "auth-token-xyz")
    }
} 