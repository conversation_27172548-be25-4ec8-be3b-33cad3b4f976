import XCTest
@testable import MonetaSDK
import Foundation

// Helper extension for AnyCodable to be Equatable for testing purposes
extension AnyCodable: Equatable {
    public static func == (lhs: AnyCodable, rhs: AnyCodable) -> Bool {
        switch (lhs.value, rhs.value) {
        case (let l as Int, let r as Int):
            return l == r
        case (let l as Double, let r as Double):
            return l == r
        case (let l as String, let r as String):
            return l == r
        case (let l as Bool, let r as Bool):
            return l == r
        case (is NSNull, is NSNull):
            return true
        case (nil, nil): // Handles AnyCodable(nil)
            return true
        // Add more cases as needed for types stored in AnyCodable
        default:
            // This default comparison is basic and might need to be more robust
            // depending on the types you store in AnyCodable.
            if let lhsComparable = lhs.value as? any Equatable,
               let rhsComparable = rhs.value as? any Equatable {
                 // Attempt to compare if both underlying values are Equatable
                 // This is tricky with type erasure. For robust testing,
                 // you might need to unwrap to specific expected types.
                return String(describing: lhsComparable) == String(describing: rhsComparable)
            }
            return false
        }
    }
}


class ArticleResponseTests: XCTestCase {

    var encoder: JSONEncoder!
    var decoder: JSONDecoder!
    let iso8601Formatter = ISO8601DateFormatter()

    override func setUpWithError() throws {
        try super.setUpWithError()
        
        iso8601Formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]

        encoder = JSONEncoder()
        // Corrected: Use .prettyPrinted, as .prettyPrintedWithoutEscapingSlashes is not standard
        encoder.outputFormatting = [.sortedKeys, .prettyPrinted] 
        encoder.dateEncodingStrategy = .custom { date, encoder in
            var container = encoder.singleValueContainer()
            try container.encode(self.iso8601Formatter.string(from: date))
        }
        
        decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .custom { decoder_param in
            let container = try decoder_param.singleValueContainer()
            let dateString = try container.decode(String.self)
            if let date = self.iso8601Formatter.date(from: dateString) {
                return date
            }
            // Keep string interpolation escaped for debug description
            throw DecodingError.dataCorruptedError(in: container, debugDescription: "Cannot decode date string \(dateString) to ISO8601Date")
        }
    }

    override func tearDownWithError() throws {
        encoder = nil
        decoder = nil
        try super.tearDownWithError()
    }

    // MARK: - Helper to create Date object from components
    private func createDate(year: Int, month: Int, day: Int, hour: Int, minute: Int, second: Int, nanosecond: Int = 0, timeZone: TimeZone = TimeZone(secondsFromGMT: 0)!) -> Date? {
        var components = DateComponents()
        components.year = year
        components.month = month
        components.day = day
        components.hour = hour
        components.minute = minute
        components.second = second
        components.nanosecond = nanosecond
        components.timeZone = timeZone
        return Calendar.current.date(from: components)
    }
    
    // MARK: - Serialization Tests

    func testArticleResponse_FullSerialization() throws {
        // Given
        let publishedDate = try XCTUnwrap(createDate(year: 2024, month: 1, day: 15, hour: 10, minute: 30, second: 0, nanosecond: 123_000_000))
        let article = ArticleResponse(
            id: "article123",
            title: "Great News!",
            summary: "A summary of the great news.",
            contentUrl: "https://example.com/news/article123",
            imageUrl: "https://example.com/images/article123.jpg",
            publishedDate: publishedDate,
            author: "John Doe",
            category: "General News",
            tags: ["exciting", "breaking"],
            metadata: ["source": AnyCodable("MonetaInternal"), "priority": AnyCodable(1), "isValid": AnyCodable(true)]
        )

        // When
        let jsonData = try encoder.encode(article)
        
        // Then
        let decodedArticle = try decoder.decode(ArticleResponse.self, from: jsonData)
        // This XCTAssertEqual requires ArticleResponse to conform to Equatable, which it does.
        XCTAssertEqual(article, decodedArticle)

        // Also verify JSON string content for key fields
        let jsonString = String(data: jsonData, encoding: .utf8)!
        XCTAssertTrue(jsonString.contains("\"article_id\" : \"article123\""))
        XCTAssertTrue(jsonString.contains("\"title\" : \"Great News!\""))
        XCTAssertTrue(jsonString.contains("\"published_date\" : \"2024-01-15T10:30:00.123Z\""))
        // Example checks for metadata content
        XCTAssertTrue(jsonString.contains("\"source\" : \"MonetaInternal\""))
        XCTAssertTrue(jsonString.contains("\"priority\" : 1"))
        XCTAssertTrue(jsonString.contains("\"isValid\" : true"))
    }
    
    func testArticleResponse_MinimalSerialization() throws {
        // Given
        // Assuming ArticleResponse has a memberwise initializer or one that handles optionals correctly.
        let article = ArticleResponse(
            id: "article456",
            title: "Minimal Update",
            summary: nil,
            contentUrl: nil,
            imageUrl: nil,
            publishedDate: nil,
            author: nil,
            category: nil,
            tags: nil,
            metadata: nil
        )

        // When
        let jsonData = try encoder.encode(article)
        
        // Then
        let decodedArticle = try decoder.decode(ArticleResponse.self, from: jsonData)
        XCTAssertEqual(article, decodedArticle)

        let jsonString = String(data: jsonData, encoding: .utf8)!
        XCTAssertTrue(jsonString.contains("\"article_id\" : \"article456\""))
        XCTAssertTrue(jsonString.contains("\"title\" : \"Minimal Update\""))
        XCTAssertFalse(jsonString.contains("\"summary\"")) // Fields with nil should not be present if encodeDefaults = false
        XCTAssertFalse(jsonString.contains("\"content_url\""))
        XCTAssertFalse(jsonString.contains("\"published_date\""))
    }

    // MARK: - Deserialization Tests
    
    func testArticleResponse_FullDeserialization() throws {
        // Given
        let jsonString = """
        {
          "article_id" : "article789",
          "title" : "Deserialized Post",
          "summary" : "Testing full deserialization.",
          "content_url" : "https://example.com/post/789",
          "image_url" : "https://example.com/images/post789.png",
          "published_date" : "2024-02-20T15:45:30.500Z",
          "author" : "Jane Smith",
          "category" : "Tech News",
          "tags" : ["swift", "codable"],
          "metadata" : {
            "isFeatured" : true,
            "views" : 1024,
            "rating" : 4.5,
            "notes" : null
          }
        }
        """
        let jsonData = Data(jsonString.utf8)
        let expectedPublishedDate = try XCTUnwrap(createDate(year: 2024, month: 2, day: 20, hour: 15, minute: 45, second: 30, nanosecond: 500_000_000))

        // When
        let decodedArticle = try decoder.decode(ArticleResponse.self, from: jsonData)

        // Then
        XCTAssertEqual(decodedArticle.id, "article789")
        XCTAssertEqual(decodedArticle.title, "Deserialized Post")
        XCTAssertEqual(decodedArticle.summary, "Testing full deserialization.")
        XCTAssertEqual(decodedArticle.contentUrl, "https://example.com/post/789")
        XCTAssertEqual(decodedArticle.imageUrl, "https://example.com/images/post789.png")
        XCTAssertEqual(decodedArticle.publishedDate, expectedPublishedDate)
        XCTAssertEqual(decodedArticle.author, "Jane Smith")
        XCTAssertEqual(decodedArticle.category, "Tech News")
        XCTAssertEqual(decodedArticle.tags, ["swift", "codable"])
        
        XCTAssertNotNil(decodedArticle.metadata)
        XCTAssertEqual(decodedArticle.metadata?["isFeatured"], AnyCodable(true))
        XCTAssertEqual(decodedArticle.metadata?["views"], AnyCodable(1024))
        XCTAssertEqual(decodedArticle.metadata?["rating"], AnyCodable(4.5))
        XCTAssertTrue(decodedArticle.metadata?.keys.contains("notes") ?? false)
        // Corrected: Use AnyCodable(nil) for explicit null
        XCTAssertEqual(decodedArticle.metadata?["notes"], AnyCodable(nil as Any?))
    }

    func testArticleResponse_Deserialization_MissingOptionals() throws {
        // Given
        let jsonString = """
        {
          "article_id" : "article101",
          "title" : "Minimal Deserialization"
        }
        """
        let jsonData = Data(jsonString.utf8)

        // When
        let decodedArticle = try decoder.decode(ArticleResponse.self, from: jsonData)

        // Then
        XCTAssertEqual(decodedArticle.id, "article101")
        XCTAssertEqual(decodedArticle.title, "Minimal Deserialization")
        XCTAssertNil(decodedArticle.summary)
        XCTAssertNil(decodedArticle.contentUrl)
        XCTAssertNil(decodedArticle.imageUrl)
        XCTAssertNil(decodedArticle.publishedDate)
        XCTAssertNil(decodedArticle.author)
        XCTAssertNil(decodedArticle.category)
        XCTAssertNil(decodedArticle.tags)
        XCTAssertNil(decodedArticle.metadata)
    }

    func testArticleResponse_Deserialization_ExplicitNullOptionals() throws {
        // Given
        let jsonString = """
        {
          "article_id" : "article112",
          "title" : "Nulls Deserialization",
          "summary" : null,
          "content_url" : null,
          "image_url" : null,
          "published_date" : null,
          "author" : null,
          "category" : null,
          "tags" : null,
          "metadata" : null
        }
        """
        let jsonData = Data(jsonString.utf8)

        // When
        let decodedArticle = try decoder.decode(ArticleResponse.self, from: jsonData)

        // Then
        XCTAssertEqual(decodedArticle.id, "article112")
        XCTAssertEqual(decodedArticle.title, "Nulls Deserialization")
        XCTAssertNil(decodedArticle.summary)
        XCTAssertNil(decodedArticle.contentUrl)
        XCTAssertNil(decodedArticle.imageUrl)
        XCTAssertNil(decodedArticle.publishedDate)
        XCTAssertNil(decodedArticle.author)
        XCTAssertNil(decodedArticle.category)
        XCTAssertNil(decodedArticle.tags)
        XCTAssertNil(decodedArticle.metadata)
    }
    
    func testArticleResponse_Deserialization_DateDecodingFailure() {
        // Given
        let jsonString = """
        {
          "article_id" : "dateFail",
          "title" : "Date Fail Test",
          "published_date" : "invalid-date-format"
        }
        """
        let jsonData = Data(jsonString.utf8)

        // When & Then
        XCTAssertThrowsError(try decoder.decode(ArticleResponse.self, from: jsonData)) { error in
            guard case DecodingError.dataCorrupted(let context) = error else {
                return XCTFail("Expected DecodingError.dataCorrupted, got \(error)")
            }
            XCTAssertTrue(context.debugDescription.contains("Cannot decode date string invalid-date-format"))
        }
    }

    func testArticleResponse_Deserialization_TypeMismatch() {
        // Given
        let jsonString = """
        {
          "article_id" : "typeFail",
          "title" : 12345 
        }
        """
        let jsonData = Data(jsonString.utf8)

        // When & Then
        XCTAssertThrowsError(try decoder.decode(ArticleResponse.self, from: jsonData)) { error in
            guard case DecodingError.typeMismatch = error else { 
                return XCTFail("Expected DecodingError.typeMismatch, got \(error)")
            }
            // More specific checks for typeMismatch can be added if needed
        }
    }
    
    func testArticleResponse_Deserialization_MissingRequiredField_Id() {
        // Given
        let jsonString = """
        {
          "title" : "Missing ID Test"
        }
        """
        let jsonData = Data(jsonString.utf8)

        // When & Then
        XCTAssertThrowsError(try decoder.decode(ArticleResponse.self, from: jsonData)) { error in
            guard case DecodingError.keyNotFound(let key, _) = error else {
                return XCTFail("Expected DecodingError.keyNotFound for id, got \(error)")
            }
            XCTAssertEqual(key.stringValue, "article_id")
        }
    }

    func testArticleResponse_Deserialization_MissingRequiredField_Title() {
        // Given
        let jsonString = """
        {
          "article_id" : "missingTitle"
        }
        """
        let jsonData = Data(jsonString.utf8)

        // When & Then
        XCTAssertThrowsError(try decoder.decode(ArticleResponse.self, from: jsonData)) { error in
            guard case DecodingError.keyNotFound(let key, _) = error else {
                return XCTFail("Expected DecodingError.keyNotFound for title, got \(error)")
            }
            XCTAssertEqual(key.stringValue, "title")
        }
    }


    // MARK: - Validation Tests

    func testArticleResponse_Validation_Valid_Full() throws {
        let article = ArticleResponse(
            id: "validId",
            title: "Valid Title",
            summary: "summary", 
            contentUrl: "https://example.com/valid", 
            imageUrl: "https://example.com/image.png", 
            publishedDate: Date(), 
            author: "author", 
            category: "category", 
            tags: ["tag1"], 
            metadata: ["key": AnyCodable("value")] 
        )
        // ArticleResponse.validate() should be available if the source file is correctly linked and compiled.
        XCTAssertNoThrow(try article.validate(), "Valid full article should not throw validation error.")
    }
    
    func testArticleResponse_Validation_Valid_Minimal() throws {
         let article = ArticleResponse(
             id: "validId",
             title: "Valid Title",
             summary: nil, contentUrl: nil, imageUrl: nil, publishedDate: nil, author: nil, category: nil, tags: nil, metadata: nil
         )
         XCTAssertNoThrow(try article.validate(), "Valid minimal article should not throw.")
     }

    func testArticleResponse_Validation_EmptyId() throws {
        let article = ArticleResponse(id: "", title: "Title", summary: nil, contentUrl: nil, imageUrl: nil, publishedDate: nil, author: nil, category: nil, tags: nil, metadata: nil)
        XCTAssertThrowsError(try article.validate()) { error in
            guard let validationError = error as? ValidationError else {
                return XCTFail("Expected ValidationError, got \(type(of: error)): \(error)")
            }
            switch validationError {
            case .emptyField(let fieldName):
                XCTAssertEqual(fieldName, "id")
            default:
                XCTFail("Unexpected ValidationError type: \(validationError)")
            }
        }
    }

    func testArticleResponse_Validation_EmptyTitle() throws {
        let article = ArticleResponse(id: "id123", title: "", summary: nil, contentUrl: nil, imageUrl: nil, publishedDate: nil, author: nil, category: nil, tags: nil, metadata: nil)
        XCTAssertThrowsError(try article.validate()) { error in
            guard let validationError = error as? ValidationError else {
                return XCTFail("Expected ValidationError, got \(type(of: error)): \(error)")
            }
            switch validationError {
            case .emptyField(let fieldName):
                XCTAssertEqual(fieldName, "title")
            default:
                XCTFail("Unexpected ValidationError type: \(validationError)")
            }
        }
    }

    func testArticleResponse_Validation_InvalidContentUrl() throws {
        let article = ArticleResponse(id: "id123", title: "Title", summary: nil, contentUrl: "not a valid url", imageUrl: nil, publishedDate: nil, author: nil, category: nil, tags: nil, metadata: nil)
        XCTAssertThrowsError(try article.validate()) { error in
            guard let validationError = error as? ValidationError else {
                return XCTFail("Expected ValidationError, got \(type(of: error)): \(error)")
            }
            // Updated to expect .invalidFormat
            switch validationError {
            case .invalidFormat(let fieldName, let expectedFormat):
                XCTAssertEqual(fieldName, "contentUrl")
                XCTAssertEqual(expectedFormat, "valid URL") // Check against the expected format string used in ArticleResponse
            default:
                XCTFail("Unexpected ValidationError type: \(validationError), expected .invalidFormat")
            }
        }
    }
    
    func testArticleResponse_Validation_ValidContentUrlButEmpty() throws {
        let article = ArticleResponse(id: "id123", title: "Title", summary: nil, contentUrl: "", imageUrl: nil, publishedDate: nil, author: nil, category: nil, tags: nil, metadata: nil)
        XCTAssertNoThrow(try article.validate(), "Article with empty optional contentUrl string should pass validation if URL(string: \"\") != nil, or if validation logic handles empty string explicitly.")
    }

    func testArticleResponse_Validation_InvalidImageUrl() throws {
        let article = ArticleResponse(id: "id123", title: "Title", summary: nil, contentUrl: nil, imageUrl: "invalid image url", publishedDate: nil, author: nil, category: nil, tags: nil, metadata: nil)
        XCTAssertThrowsError(try article.validate()) { error in
            guard let validationError = error as? ValidationError else {
                return XCTFail("Expected ValidationError, got \(type(of: error)): \(error)")
            }
            // Updated to expect .invalidFormat
            switch validationError {
            case .invalidFormat(let fieldName, let expectedFormat):
                XCTAssertEqual(fieldName, "imageUrl")
                XCTAssertEqual(expectedFormat, "valid URL") // Check against the expected format string
            default:
                XCTFail("Unexpected ValidationError type: \(validationError), expected .invalidFormat")
            }
        }
    }
    
    func testArticleResponse_Validation_ValidImageUrlButEmpty() throws {
        let article = ArticleResponse(id: "id123", title: "Title", summary: nil, contentUrl: nil, imageUrl: "", publishedDate: nil, author: nil, category: nil, tags: nil, metadata: nil)
        XCTAssertNoThrow(try article.validate(), "Article with empty optional imageUrl string should pass.")
    }
}

} 