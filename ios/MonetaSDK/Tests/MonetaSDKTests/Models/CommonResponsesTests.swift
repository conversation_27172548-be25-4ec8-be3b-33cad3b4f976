import XCTest
@testable import MonetaSDK

final class CommonResponsesTests: XCTestCase {
    
    // MARK: - ApiResponse Tests
    
    func testApiResponseWithDataDecoding() throws {
        let json = """
        {
            "statusCode": 200,
            "data": {
                "id": "test-id",
                "name": "test-name"
            },
            "message": "Success"
        }
        """.data(using: .utf8)!
        
        let response = try JSONDecoder.apiDecoder.decode(ApiResponse<TestModel>.self, from: json)
        
        XCTAssertEqual(response.statusCode, 200)
        XCTAssertEqual(response.data?.id, "test-id")
        XCTAssertEqual(response.data?.name, "test-name")
        XCTAssertEqual(response.message, "Success")
        XCTAssertNil(response.error)
    }
    
    func testApiResponseWithErrorDecoding() throws {
        let json = """
        {
            "statusCode": 400,
            "error": {
                "code": "invalid_request",
                "details": "Missing required field"
            },
            "message": "Bad Request"
        }
        """.data(using: .utf8)!
        
        let response = try JSONDecoder.apiDecoder.decode(ApiResponse<TestModel>.self, from: json)
        
        XCTAssertEqual(response.statusCode, 400)
        XCTAssertNil(response.data)
        XCTAssertEqual(response.message, "Bad Request")
        XCTAssertNotNil(response.error)
        // We can't directly access the error properties since it's stored as [String: Any]
    }
    
    func testApiResponseEncoding() throws {
        let originalResponse = ApiResponse<TestModel>(
            statusCode: 200,
            data: TestModel(id: "test-id", name: "test-name"),
            error: nil,
            message: "Success"
        )
        
        let encodedData = try JSONEncoder.apiEncoder.encode(originalResponse)
        let decodedResponse = try JSONDecoder.apiDecoder.decode(ApiResponse<TestModel>.self, from: encodedData)
        
        XCTAssertEqual(decodedResponse.statusCode, originalResponse.statusCode)
        XCTAssertEqual(decodedResponse.data?.id, originalResponse.data?.id)
        XCTAssertEqual(decodedResponse.data?.name, originalResponse.data?.name)
        XCTAssertEqual(decodedResponse.message, originalResponse.message)
    }
    
    // MARK: - PaginatedResponse Tests
    
    func testPaginatedResponseDecoding() throws {
        let json = """
        {
            "data": [
                {
                    "id": "item-1",
                    "name": "Item 1"
                },
                {
                    "id": "item-2",
                    "name": "Item 2"
                }
            ],
            "links": {
                "prev": "https://api.example.com/items?page=1",
                "next": "https://api.example.com/items?page=3"
            },
            "metadata": {
                "count": 2,
                "totalCount": 50,
                "totalAmount": 1000.50,
                "unreadApprovals": 5,
                "unreadAlerts": 3
            }
        }
        """.data(using: .utf8)!
        
        let response = try JSONDecoder.apiDecoder.decode(PaginatedResponse<TestModel>.self, from: json)
        
        XCTAssertEqual(response.data.count, 2)
        XCTAssertEqual(response.data[0].id, "item-1")
        XCTAssertEqual(response.data[1].name, "Item 2")
        
        XCTAssertEqual(response.links.prev, "https://api.example.com/items?page=1")
        XCTAssertEqual(response.links.next, "https://api.example.com/items?page=3")
        
        XCTAssertEqual(response.metadata.count, 2)
        XCTAssertEqual(response.metadata.totalCount, 50)
        XCTAssertEqual(response.metadata.totalAmount, 1000.50)
        XCTAssertEqual(response.metadata.unreadApprovals, 5)
        XCTAssertEqual(response.metadata.unreadAlerts, 3)
    }
    
    func testPaginatedResponseEncoding() throws {
        let originalResponse = PaginatedResponse(
            data: [
                TestModel(id: "item-1", name: "Item 1"),
                TestModel(id: "item-2", name: "Item 2")
            ],
            links: Links(prev: "prev-link", next: "next-link"),
            metadata: PaginationMetadata(
                count: 2,
                totalCount: 50,
                totalAmount: 1000.50,
                unreadApprovals: 5,
                unreadAlerts: 3
            )
        )
        
        let encodedData = try JSONEncoder.apiEncoder.encode(originalResponse)
        let decodedResponse = try JSONDecoder.apiDecoder.decode(PaginatedResponse<TestModel>.self, from: encodedData)
        
        XCTAssertEqual(decodedResponse.data.count, originalResponse.data.count)
        XCTAssertEqual(decodedResponse.links.prev, originalResponse.links.prev)
        XCTAssertEqual(decodedResponse.links.next, originalResponse.links.next)
        XCTAssertEqual(decodedResponse.metadata.count, originalResponse.metadata.count)
        XCTAssertEqual(decodedResponse.metadata.totalCount, originalResponse.metadata.totalCount)
        XCTAssertEqual(decodedResponse.metadata.totalAmount, originalResponse.metadata.totalAmount)
    }
}

// MARK: - Helper Models

private struct TestModel: Codable, Equatable {
    let id: String
    let name: String
} 