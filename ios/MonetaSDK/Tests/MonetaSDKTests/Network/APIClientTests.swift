import XCTest
import Foundation
@testable import MonetaSDK

// MARK: - Protocols for Testing

/// Protocol for URLSession abstraction to facilitate testing
protocol URLSessionProtocol {
    func dataTask(with request: URLRequest, completionHandler: @escaping @Sendable (Data?, URLResponse?, Error?) -> Void) -> URLSessionDataTaskProtocol
}

/// Protocol for URLSessionDataTask abstraction
protocol URLSessionDataTaskProtocol {
    func cancel()
    func resume()
}

// Adapt URLSessionDataTask to conform to our protocol
extension URLSessionDataTask: URLSessionDataTaskProtocol {}

// MARK: - Mock Types for Testing

// Define a mock of the necessary components for testing
enum MockAPIError: Error, Equatable {
    case invalidURL
    case invalidResponse
    case badRequest
    case unauthorized
    case forbidden
    case notFound
    case serverError(Int)
    case decodingFailed(String)
    case networkError(String)
    case unexpectedStatusCode(Int)
}

// Mock HTTP methods for testing
enum MockHTTPMethod: String {
    case get = "GET"
    case post = "POST"
    case put = "PUT"
    case delete = "DELETE"
}

// Mock URL Session for testing
class MockURLSession: URLSessionProtocol {
    var mockResponse: (Data, URLResponse, Error?)!
    var lastRequest: URLRequest?
    var mockDataTask = MockURLSessionDataTask()
    
    func dataTask(with request: URLRequest, completionHandler: @escaping @Sendable (Data?, URLResponse?, Error?) -> Void) -> URLSessionDataTaskProtocol {
        lastRequest = request
        completionHandler(mockResponse.0, mockResponse.1, mockResponse.2)
        return mockDataTask
    }
}

// Simple mock implementation that doesn't inherit from URLSessionDataTask
class MockURLSessionDataTask: URLSessionDataTaskProtocol {
    var cancelCalled = false
    
    func cancel() {
        cancelCalled = true
    }
    
    func resume() {
        // No-op for tests
    }
}

// Define protocol for mock client
protocol MockAPIClient {
    var baseURL: URL { get }
    
    func get<T: Decodable>(
        endpoint: String,
        parameters: [String: Any]?,
        headers: [String: String]?
    ) async throws -> T
    
    func post<T: Decodable, U: Encodable>(
        endpoint: String,
        body: U?,
        headers: [String: String]?
    ) async throws -> T
    
    func put<T: Decodable, U: Encodable>(
        endpoint: String,
        body: U?,
        headers: [String: String]?
    ) async throws -> T
    
    func delete<T: Decodable>(
        endpoint: String,
        parameters: [String: Any]?,
        headers: [String: String]?
    ) async throws -> T
    
    func cancelAllRequests()
    
    func setTimeout(seconds: TimeInterval)
}

// Simple implementation for testing
class MockAPIClientImpl: MockAPIClient {
    let baseURL: URL
    let mockSession: MockURLSession
    
    init(baseURL: URL, mockSession: MockURLSession) {
        self.baseURL = baseURL
        self.mockSession = mockSession
    }
    
    func get<T: Decodable>(
        endpoint: String,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        // Simulate request creation
        var components = URLComponents(url: baseURL.appendingPathComponent(endpoint), resolvingAgainstBaseURL: false)
        
        if let parameters = parameters, !parameters.isEmpty {
            components?.queryItems = parameters.map { key, value in
                URLQueryItem(name: key, value: String(describing: value))
            }
        }
        
        var request = URLRequest(url: components?.url ?? baseURL)
        request.httpMethod = "GET"
        
        if let headers = headers {
            for (key, value) in headers {
                request.setValue(value, forHTTPHeaderField: key)
            }
        }
        
        // Use mockSession to handle the request
        return try await withCheckedThrowingContinuation { continuation in
            let completionHandler: @Sendable (Data?, URLResponse?, Error?) -> Void = { data, response, error in
                if let error = error {
                    continuation.resume(throwing: MockAPIError.networkError(error.localizedDescription))
                    return
                }
                
                guard let data = data, let response = response as? HTTPURLResponse else {
                    continuation.resume(throwing: MockAPIError.invalidResponse)
                    return
                }
                
                switch response.statusCode {
                case 200..<300:
                    do {
                        let decoded = try JSONDecoder().decode(T.self, from: data)
                        continuation.resume(returning: decoded)
                    } catch {
                        continuation.resume(throwing: MockAPIError.decodingFailed(error.localizedDescription))
                    }
                case 400:
                    continuation.resume(throwing: MockAPIError.badRequest)
                case 401:
                    continuation.resume(throwing: MockAPIError.unauthorized)
                case 403:
                    continuation.resume(throwing: MockAPIError.forbidden)
                case 404:
                    continuation.resume(throwing: MockAPIError.notFound)
                case 500..<600:
                    continuation.resume(throwing: MockAPIError.serverError(response.statusCode))
                default:
                    continuation.resume(throwing: MockAPIError.unexpectedStatusCode(response.statusCode))
                }
            }
            
            mockSession.dataTask(with: request, completionHandler: completionHandler).resume()
        }
    }
    
    func post<T: Decodable, U: Encodable>(
        endpoint: String,
        body: U? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        // Implementation would be similar to get
        return try await get(endpoint: endpoint, parameters: nil, headers: headers)
    }
    
    func put<T: Decodable, U: Encodable>(
        endpoint: String,
        body: U? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        // Implementation would be similar to get
        return try await get(endpoint: endpoint, parameters: nil, headers: headers)
    }
    
    func delete<T: Decodable>(
        endpoint: String,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        // Implementation would be similar to get
        return try await get(endpoint: endpoint, parameters: parameters, headers: headers)
    }
    
    func cancelAllRequests() {
        mockSession.mockDataTask.cancel()
    }
    
    func setTimeout(seconds: TimeInterval) {
        // No-op for mock
    }
}

// MARK: - Actual Test Cases

final class APIClientTests: XCTestCase {
    var sut: MockAPIClientImpl!
    var mockSession: MockURLSession!
    var baseURL: URL!
    
    override func setUp() {
        super.setUp()
        baseURL = URL(string: "https://api.moneta.test")!
        mockSession = MockURLSession()
        sut = MockAPIClientImpl(baseURL: baseURL, mockSession: mockSession)
    }
    
    override func tearDown() {
        sut = nil
        mockSession = nil
        baseURL = nil
        super.tearDown()
    }
    
    // MARK: - GET Tests
    
    func testGetRequest_Success() async throws {
        // Arrange
        let expectedResponse = MockResponse(id: "123", name: "Test")
        let jsonData = try JSONEncoder().encode(expectedResponse)
        mockSession.mockResponse = (jsonData, HTTPURLResponse(url: baseURL, statusCode: 200, httpVersion: nil, headerFields: nil)!, nil)
        
        // Act
        let response: MockResponse = try await sut.get(endpoint: "test")
        
        // Assert
        XCTAssertEqual(response.id, expectedResponse.id)
        XCTAssertEqual(response.name, expectedResponse.name)
        XCTAssertEqual(mockSession.lastRequest?.url?.path, "/test")
        XCTAssertEqual(mockSession.lastRequest?.httpMethod, "GET")
    }
    
    func testGetRequest_WithParameters() async throws {
        // Arrange
        let parameters = ["key1": "value1", "key2": 2] as [String: Any]
        let expectedResponse = MockResponse(id: "123", name: "Test")
        let jsonData = try JSONEncoder().encode(expectedResponse)
        mockSession.mockResponse = (jsonData, HTTPURLResponse(url: baseURL, statusCode: 200, httpVersion: nil, headerFields: nil)!, nil)
        
        // Act
        let _: MockResponse = try await sut.get(endpoint: "test", parameters: parameters)
        
        // Assert
        let url = mockSession.lastRequest?.url
        let components = URLComponents(url: url!, resolvingAgainstBaseURL: false)
        let queryItems = components?.queryItems
        
        XCTAssertEqual(mockSession.lastRequest?.httpMethod, "GET")
        XCTAssertTrue(queryItems?.contains(where: { $0.name == "key1" && $0.value == "value1" }) ?? false)
        XCTAssertTrue(queryItems?.contains(where: { $0.name == "key2" && $0.value == "2" }) ?? false)
    }
    
    func testGetRequest_WithHeaders() async throws {
        // Arrange
        let headers = ["X-Custom-Header": "CustomValue"]
        let expectedResponse = MockResponse(id: "123", name: "Test")
        let jsonData = try JSONEncoder().encode(expectedResponse)
        mockSession.mockResponse = (jsonData, HTTPURLResponse(url: baseURL, statusCode: 200, httpVersion: nil, headerFields: nil)!, nil)
        
        // Act
        let _: MockResponse = try await sut.get(endpoint: "test", headers: headers)
        
        // Assert
        XCTAssertEqual(mockSession.lastRequest?.value(forHTTPHeaderField: "X-Custom-Header"), "CustomValue")
    }
    
    func testGetRequest_NotFound() async {
        // Arrange
        mockSession.mockResponse = (Data(), HTTPURLResponse(url: baseURL, statusCode: 404, httpVersion: nil, headerFields: nil)!, nil)
        
        // Act & Assert
        do {
            let _: MockResponse = try await sut.get(endpoint: "test")
            XCTFail("Expected error to be thrown")
        } catch let error as MockAPIError {
            XCTAssertEqual(error, .notFound)
        } catch {
            XCTFail("Unexpected error type: \(error)")
        }
    }
    
    func testGetRequest_ServerError() async {
        // Arrange
        mockSession.mockResponse = (Data(), HTTPURLResponse(url: baseURL, statusCode: 500, httpVersion: nil, headerFields: nil)!, nil)
        
        // Act & Assert
        do {
            let _: MockResponse = try await sut.get(endpoint: "test")
            XCTFail("Expected error to be thrown")
        } catch let error as MockAPIError {
            if case .serverError(let code) = error {
                XCTAssertEqual(code, 500)
            } else {
                XCTFail("Wrong error type: \(error)")
            }
        } catch {
            XCTFail("Unexpected error type: \(error)")
        }
    }
    
    // MARK: - Cancellation Tests
    
    func testCancelAllRequests() {
        // Act - call cancel
        sut.cancelAllRequests()
        
        // Assert
        XCTAssertTrue(mockSession.mockDataTask.cancelCalled)
    }
}

// MARK: - Helper Types

struct MockRequest: Codable {
    let name: String
}

struct MockResponse: Codable, Equatable {
    let id: String
    let name: String
} 
