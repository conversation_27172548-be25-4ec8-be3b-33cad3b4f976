import Foundation
import XCTest
@testable import MonetaSDK

// Mock implementations of internal cache classes for testing

class MockCacheManager {
    private var metadataCache = [String: TestCacheMetadata]()
    private let cacheDir: URL
    
    init(cacheDir: URL) {
        self.cacheDir = cacheDir
        
        if !FileManager.default.fileExists(atPath: cacheDir.path) {
            do {
                try FileManager.default.createDirectory(at: cacheDir, withIntermediateDirectories: true)
            } catch {
                // Ignore errors in tests
            }
        }
    }
    
    @discardableResult
    func cacheResponse(
        cacheKey: String,
        data: Data,
        cacheDuration: TimeInterval,
        headers: [String: String]
    ) -> Bool {
        do {
            let fileKey = generateFileKey(cacheKey: cacheKey)
            let cacheFile = cacheDir.appendingPathComponent(fileKey)
            
            try data.write(to: cacheFile)
            
            let expiryTime = Date().timeIntervalSince1970 + cacheDuration
            let metadata = TestCacheMetadata(
                key: cacheKey,
                fileKey: fileKey,
                expiryTime: expiryTime,
                lastModified: Date().timeIntervalSince1970,
                eTag: headers["ETag"]
            )
            
            metadataCache[cacheKey] = metadata
            return true
        } catch {
            return false
        }
    }
    
    func getCachedResponse(cacheKey: String) -> TestCachedResponse? {
        guard let metadata = metadataCache[cacheKey] else { return nil }
        
        if Date().timeIntervalSince1970 > metadata.expiryTime {
            removeCache(cacheKey: cacheKey)
            return nil
        }
        
        do {
            let cacheFile = cacheDir.appendingPathComponent(metadata.fileKey)
            let data = try Data(contentsOf: cacheFile)
            return TestCachedResponse(data: data, metadata: metadata)
        } catch {
            return nil
        }
    }
    
    @discardableResult
    func removeCache(cacheKey: String) -> Bool {
        guard let metadata = metadataCache[cacheKey] else { return false }
        
        do {
            let cacheFile = cacheDir.appendingPathComponent(metadata.fileKey)
            if FileManager.default.fileExists(atPath: cacheFile.path) {
                try FileManager.default.removeItem(at: cacheFile)
            }
            
            metadataCache.removeValue(forKey: cacheKey)
            return true
        } catch {
            return false
        }
    }
    
    @discardableResult
    func clearCache() -> Bool {
        do {
            metadataCache.removeAll()
            
            let fileManager = FileManager.default
            let files = try fileManager.contentsOfDirectory(at: cacheDir, includingPropertiesForKeys: nil)
            
            for file in files {
                try fileManager.removeItem(at: file)
            }
            
            return true
        } catch {
            return false
        }
    }
    
    private func generateFileKey(cacheKey: String) -> String {
        return UUID().uuidString
    }
}

class MockCachingRequestInterceptor {
    private var defaultCacheDuration: TimeInterval = 30 * 60 // 30 minutes
    private var forceNetworkValue: Bool = false
    private var forceCacheValue: Bool = false
    private var excludedEndpoints = Set<String>()
    private let cacheDir: URL
    
    init(cacheDir: URL) {
        self.cacheDir = cacheDir
    }
    
    func setDefaultCacheDuration(seconds: TimeInterval) {
        self.defaultCacheDuration = seconds
    }
    
    func forceNetwork(force: Bool) {
        self.forceNetworkValue = force
        if force {
            self.forceCacheValue = false
        }
    }
    
    func forceCache(force: Bool) {
        self.forceCacheValue = force
        if force {
            self.forceNetworkValue = false
        }
    }
    
    func excludeEndpoint(_ endpoint: String) {
        excludedEndpoints.insert(endpoint)
    }
    
    @discardableResult
    func clearCache() -> Bool {
        return true
    }
    
    func intercept(request: URLRequest) -> URLRequest {
        guard request.httpMethod == "GET" else {
            return request
        }
        
        guard let url = request.url?.absoluteString else {
            return request
        }
        
        // Check if any excluded endpoint is contained in the URL
        for endpoint in excludedEndpoints {
            if url.contains(endpoint) {
                // Just return the original request without modification for excluded endpoints
                return request
            }
        }
        
        var mutableRequest = request
        
        if forceNetworkValue {
            mutableRequest.cachePolicy = .reloadIgnoringLocalCacheData
            mutableRequest.addValue("no-cache", forHTTPHeaderField: "Cache-Control")
        } else if forceCacheValue {
            mutableRequest.cachePolicy = .returnCacheDataElseLoad
            mutableRequest.addValue("max-stale=86400", forHTTPHeaderField: "Cache-Control")
        } else {
            mutableRequest.cachePolicy = .useProtocolCachePolicy
            mutableRequest.addValue("max-age=\(Int(defaultCacheDuration))", forHTTPHeaderField: "Cache-Control")
        }
        
        return mutableRequest
    }
}

class MockCachingResponseInterceptor {
    private var defaultCacheDuration: TimeInterval = 30 * 60 // 30 minutes
    private var excludedEndpoints = Set<String>()
    private let cacheManager: MockCacheManager
    private let cacheDir: URL
    
    init(cacheDir: URL) {
        self.cacheDir = cacheDir
        self.cacheManager = MockCacheManager(cacheDir: cacheDir)
    }
    
    func setDefaultCacheDuration(seconds: TimeInterval) {
        self.defaultCacheDuration = seconds
    }
    
    func excludeEndpoint(_ endpoint: String) {
        excludedEndpoints.insert(endpoint)
    }
    
    @discardableResult
    func clearCache() -> Bool {
        return cacheManager.clearCache()
    }
    
    func intercept(response: URLResponse, data: Data, request: URLRequest) -> (URLResponse, Data) {
        guard request.httpMethod == "GET" else {
            return (response, data)
        }
        
        guard let url = request.url?.absoluteString else {
            return (response, data)
        }
        
        if excludedEndpoints.contains(where: { url.contains($0) }) {
            return (response, data)
        }
        
        guard let httpResponse = response as? HTTPURLResponse else {
            return (response, data)
        }
        
        guard (200...299).contains(httpResponse.statusCode) else {
            return (response, data)
        }
        
        let cacheKey = generateCacheKey(request: request)
        var cacheDuration = defaultCacheDuration
        
        if let cacheControlHeader = httpResponse.value(forHTTPHeaderField: "Cache-Control") {
            if cacheControlHeader.contains("no-store") {
                return (response, data)
            }
            
            let maxAgePattern = "max-age=(\\d+)"
            if let regex = try? NSRegularExpression(pattern: maxAgePattern),
               let match = regex.firstMatch(in: cacheControlHeader, range: NSRange(cacheControlHeader.startIndex..., in: cacheControlHeader)),
               let maxAgeRange = Range(match.range(at: 1), in: cacheControlHeader),
               let maxAge = Int(cacheControlHeader[maxAgeRange]) {
                cacheDuration = TimeInterval(maxAge)
            }
        }
        
        let headers = httpResponse.allHeaderFields.reduce(into: [String: String]()) { result, pair in
            if let key = pair.key as? String, let value = pair.value as? String {
                result[key] = value
            }
        }
        
        cacheManager.cacheResponse(cacheKey: cacheKey, data: data, cacheDuration: cacheDuration, headers: headers)
        
        return (response, data)
    }
    
    func generateCacheKey(request: URLRequest) -> String {
        let url = request.url?.absoluteString ?? ""
        let method = request.httpMethod ?? "GET"
        return "\(method)-\(url)"
    }
    
    func getCachedResponse(cacheKey: String) -> TestCachedResponse? {
        return cacheManager.getCachedResponse(cacheKey: cacheKey)
    }
} 