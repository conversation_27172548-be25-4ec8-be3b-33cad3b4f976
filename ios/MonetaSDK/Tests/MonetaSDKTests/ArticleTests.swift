import XCTest
@testable import MonetaSDK

final class ArticleTests: XCTestCase {
    
    func testArticleInitialization() {
        // Given
        let id = "article-123"
        let title = "Test Article"
        let content = "This is test content"
        let url = URL(string: "https://example.com/articles/123")!
        let publishedAt = Date()
        let author = "Test Author"
        let categories = ["Technology", "News"]
        let imageUrl = URL(string: "https://example.com/images/123.jpg")
        
        // When
        let article = Article(
            id: id,
            title: title,
            content: content,
            url: url,
            publishedAt: publishedAt,
            author: author,
            categories: categories,
            imageUrl: imageUrl
        )
        
        // Then
        XCTAssertEqual(article.id, id)
        XCTAssertEqual(article.title, title)
        XCTAssertEqual(article.content, content)
        XCTAssertEqual(article.url, url)
        XCTAssertEqual(article.publishedAt, publishedAt)
        XCTAssertEqual(article.author, author)
        XCTAssertEqual(article.categories, categories)
        XCTAssertEqual(article.imageUrl, imageUrl)
        XCTAssertEqual(article.version, 1) // Default version
    }
    
    func testArticleSerialization() throws {
        // Given
        let id = "article-123"
        let title = "Test Article"
        let content = "This is test content"
        let url = URL(string: "https://example.com/articles/123")!
        let publishedAt = Date(timeIntervalSince1970: 1609459200) // 2021-01-01
        let author = "Test Author"
        let categories = ["Technology", "News"]
        let imageUrl = URL(string: "https://example.com/images/123.jpg")!
        
        let article = Article(
            id: id,
            title: title,
            content: content,
            url: url,
            publishedAt: publishedAt,
            author: author,
            categories: categories,
            imageUrl: imageUrl
        )
        
        // When
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let data = try encoder.encode(article)
        
        // Then
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        let decodedArticle = try decoder.decode(Article.self, from: data)
        
        XCTAssertEqual(decodedArticle.id, id)
        XCTAssertEqual(decodedArticle.title, title)
        XCTAssertEqual(decodedArticle.content, content)
        XCTAssertEqual(decodedArticle.url, url)
        XCTAssertEqual(decodedArticle.publishedAt, publishedAt)
        XCTAssertEqual(decodedArticle.author, author)
        XCTAssertEqual(decodedArticle.categories, categories)
        XCTAssertEqual(decodedArticle.imageUrl, imageUrl)
        XCTAssertEqual(decodedArticle.version, 1)
    }
    
    func testArticleDeserialization() throws {
        // Given
        let json = """
        {
            "id": "article-123",
            "title": "Test Article",
            "content": "This is test content",
            "url": "https://example.com/articles/123",
            "publishedAt": "2021-01-01T00:00:00Z",
            "author": "Test Author",
            "categories": ["Technology", "News"],
            "imageUrl": "https://example.com/images/123.jpg",
            "version": 1
        }
        """
        
        // When
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        let data = json.data(using: .utf8)!
        let article = try decoder.decode(Article.self, from: data)
        
        // Then
        XCTAssertEqual(article.id, "article-123")
        XCTAssertEqual(article.title, "Test Article")
        XCTAssertEqual(article.content, "This is test content")
        XCTAssertEqual(article.url, URL(string: "https://example.com/articles/123")!)
        
        // Create expected date
        let dateFormatter = ISO8601DateFormatter()
        let expectedDate = dateFormatter.date(from: "2021-01-01T00:00:00Z")!
        XCTAssertEqual(article.publishedAt, expectedDate)
        
        XCTAssertEqual(article.author, "Test Author")
        XCTAssertEqual(article.categories, ["Technology", "News"])
        XCTAssertEqual(article.imageUrl, URL(string: "https://example.com/images/123.jpg")!)
        XCTAssertEqual(article.version, 1)
    }
    
    func testVersionedModelImplementation() {
        // Given
        let article = Article(
            id: "article-123",
            title: "Test Article",
            content: "This is test content",
            url: URL(string: "https://example.com/articles/123")!,
            publishedAt: Date(),
            author: "Test Author",
            categories: ["Technology", "News"],
            version: 1
        )
        
        // When
        let latestVersion = article.toLatestVersion()
        
        // Then
        XCTAssertEqual(article, latestVersion)
    }
    
    func testArticleVersionEnum() {
        // Given
        let article = Article(
            id: "article-123",
            title: "Test Article",
            content: "This is test content",
            url: URL(string: "https://example.com/articles/123")!,
            publishedAt: Date(),
            author: "Test Author",
            categories: ["Technology", "News"]
        )
        
        // When
        let articleVersion = ArticleVersion.v1(article)
        
        // Then
        XCTAssertEqual(articleVersion.latest, article)
    }
    
    func testEquatableImplementation() {
        // Given
        let date = Date()
        let article1 = Article(
            id: "article-123",
            title: "Test Article",
            content: "This is test content",
            url: URL(string: "https://example.com/articles/123")!,
            publishedAt: date,
            author: "Test Author",
            categories: ["Technology", "News"]
        )
        
        let article2 = Article(
            id: "article-123",
            title: "Test Article",
            content: "This is test content",
            url: URL(string: "https://example.com/articles/123")!,
            publishedAt: date,
            author: "Test Author",
            categories: ["Technology", "News"]
        )
        
        let article3 = Article(
            id: "article-456",
            title: "Different Article",
            content: "Different content",
            url: URL(string: "https://example.com/articles/456")!,
            publishedAt: date,
            author: "Different Author",
            categories: ["Science"]
        )
        
        // Then
        XCTAssertEqual(article1, article2)
        XCTAssertNotEqual(article1, article3)
    }
    
    func testHashableImplementation() {
        // Given
        let date = Date()
        let article1 = Article(
            id: "article-123",
            title: "Test Article",
            content: "This is test content",
            url: URL(string: "https://example.com/articles/123")!,
            publishedAt: date,
            author: "Test Author",
            categories: ["Technology", "News"]
        )
        
        let article2 = Article(
            id: "article-123",
            title: "Test Article",
            content: "This is test content",
            url: URL(string: "https://example.com/articles/123")!,
            publishedAt: date,
            author: "Test Author",
            categories: ["Technology", "News"]
        )
        
        // When
        var articleSet = Set<Article>()
        articleSet.insert(article1)
        articleSet.insert(article2)
        
        // Then
        XCTAssertEqual(articleSet.count, 1)
    }
} 