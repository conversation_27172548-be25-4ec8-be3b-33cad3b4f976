import Testing
@testable import MonetaSDK

// Helper for tests - using different name to avoid conflicts
enum StorageTestError: Error {
    case dummy
}

// MARK: - StorageError Tests

@Test func testStorageError_LocalizedDescription() {
    #expect(StorageError.encodingFailed(key: "k").localizedDescription.contains("encode"))
    #expect(StorageError.decodingFailed(key: "k").localizedDescription.contains("decode"))
    #expect(StorageError.saveFailed(key: "k").localizedDescription.contains("save"))
    #expect(StorageError.fetchFailed(key: "k").localizedDescription.contains("fetch"))
    #expect(StorageError.deleteFailed(key: "k").localizedDescription.contains("delete"))
    #expect(StorageError.keychainError(osStatus: -50, operation: "add", key: "k", message: "errSecParam").localizedDescription.contains("Keychain operation 'add' failed"))
    #expect(StorageError.unknown().localizedDescription.contains("unknown storage error"))
}

@Test func testStorageError_ErrorCode() {
    #expect(StorageError.encodingFailed(key: "").errorCode == 2001)
    #expect(StorageError.decodingFailed(key: "").errorCode == 2002)
    #expect(StorageError.saveFailed(key: "").errorCode == 2003)
    #expect(StorageError.fetchFailed(key: "").errorCode == 2004)
    #expect(StorageError.deleteFailed(key: "").errorCode == 2005)
    #expect(StorageError.keychainError(osStatus: 0, operation: "", key: "", message: "").errorCode == 2006)
    #expect(StorageError.unknown().errorCode == 2999)
}

@Test func testStorageError_Equatable() {
    #expect(
        StorageError.saveFailed(key: "a") ==
        StorageError.saveFailed(key: "b", message: "detail") // Should be equal based on errorCode only
    )
    #expect(
        StorageError.encodingFailed(key: "a") !=
        StorageError.fetchFailed(key: "a")
    )
} 