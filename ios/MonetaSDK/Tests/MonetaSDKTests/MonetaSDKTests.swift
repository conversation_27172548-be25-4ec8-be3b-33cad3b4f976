import XCTest
@testable import MonetaSDK
import Foundation

final class MonetaSDKTests: XCTestCase {
    
    var sdk: MonetaSDK!
    
    override func setUp() {
        super.setUp()
        // Get a shared instance for testing
        sdk = MonetaSDK.getInstance(appId: "test-app-id")
    }
    
    override func tearDown() {
        // Clean up
        super.tearDown()
    }
    
    func testVersionConstant() {
        // Simply ensure version constant exists and is a non-empty string
        XCTAssertFalse(MonetaSDK.version.isEmpty)
    }
    
    func testSecureConfiguration() {
        // Test that we can get a secure configuration without errors
        let config = sdk.getSecureSessionConfiguration()
        
        // Basic assertions that don't depend on specific enum types
        XCTAssertGreaterThan(config.timeoutIntervalForRequest, 0)
        XCTAssertGreaterThan(config.timeoutIntervalForResource, 0)
        
        // Check header exists without specifying the type
        XCTAssertNotNil(config.httpAdditionalHeaders?["X-Content-Type-Options"])
    }
    
    func testCreateSecureSession() {
        // Test that we can create a secure session without errors
        let session = sdk.createSecureURLSession()
        
        // Basic assertions
        XCTAssertNotNil(session)
        XCTAssertGreaterThan(session.configuration.timeoutIntervalForRequest, 0)
    }
    
    func testCertificatePinning() {
        // Test certificate pinning configuration doesn't throw
        let testCerts = ["sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA="]
        sdk.configureCertificatePinning(certificates: testCerts)
        
        // No assertions - just verifying it doesn't crash
    }
    
    func testCachingMethods() {
        // Just test that these methods don't throw
        sdk.configureCaching(cacheDuration: 300)
        sdk.forceNetworkRequests(true)
        sdk.forceCachedResponses(false)
        sdk.excludeEndpointFromCaching("/api/test")
        let _ = sdk.clearCache()
    }
}
