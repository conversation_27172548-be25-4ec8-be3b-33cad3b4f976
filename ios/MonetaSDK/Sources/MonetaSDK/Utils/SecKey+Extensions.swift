import Foundation
import Security

extension <PERSON><PERSON><PERSON><PERSON> {
    /// Returns the DER representation of the key
    var derRepresentation: Data {
        var error: Unmanaged<CFError>?
        guard let keyData = SecKeyCopyExternalRepresentation(self, &error) else {
            // If we can't get the external representation, return empty data
            // In a production app, you might want to throw an error instead
            return Data()
        }
        return keyData as Data
    }
}
