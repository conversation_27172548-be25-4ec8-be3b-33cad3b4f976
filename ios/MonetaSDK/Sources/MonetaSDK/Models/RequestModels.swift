import Foundation

// Request Models
public struct OnboardingRequest: Codable {
    public let deviceName: String
    public let deviceToken: String
    public let publicKey: String
    
    public init(deviceName: String, deviceToken: String, publicKey: String) {
        self.deviceName = deviceName
        self.deviceToken = deviceToken
        self.publicKey = publicKey
    }
    
    enum CodingKeys: String, CodingKey {
        case deviceName = "deviceName"
        case deviceToken = "deviceToken"
        case publicKey = "publicKey"
    }
}

public struct ApprovalSubscriptionChargeRequest: Codable {
    public let status: String // enum: approved, rejected
    
    public init(status: String) {
        self.status = status
    }
    
    enum CodingKeys: String, CodingKey {
        case status = "status"
    }
}

public struct CreateDisputeRequest: Codable {
    public let transactionId: String
    public let reason: String
    public let itemId: String?
    
    public init(transactionId: String, reason: String, itemId: String? = nil) {
        self.transactionId = transactionId
        self.reason = reason
        self.itemId = itemId
    }
    
    enum CodingKeys: String, CodingKey {
        case transactionId = "transactionId"
        case reason = "reason"
        case itemId = "itemId"
    }
}

public struct PublisherLoginRequest: Codable {
    public let session: String
    
    public init(session: String) {
        self.session = session
    }
    
    enum CodingKeys: String, CodingKey {
        case session = "pinet_session"
    }
}

public struct RecommendationsRequest: Codable {
    public let userId: String
    public let userProfile: UserProfile
    public let offset: Int
    public let limit: Int
    
    public init(userId: String, userProfile: UserProfile, offset: Int, limit: Int) {
        self.userId = userId
        self.userProfile = userProfile
        self.offset = offset
        self.limit = limit
    }
    
    enum CodingKeys: String, CodingKey {
        case userId = "user_id"
        case userProfile = "user_profile"
        case offset = "offset"
        case limit = "limit"
    }
}

public struct UserProfile: Codable {
    public let demographics: [String: AnyCodable]?
    public let contentPreferences: ContentPreferences
    
    public init(demographics: [String: AnyCodable]? = nil, contentPreferences: ContentPreferences) {
        self.demographics = demographics
        self.contentPreferences = contentPreferences
    }
    
    enum CodingKeys: String, CodingKey {
        case demographics = "demographics"
        case contentPreferences = "content_preferences"
    }
}

public struct ContentPreferences: Codable {
    public let interests: [String]
    
    public init(interests: [String]) {
        self.interests = interests
    }
    
    enum CodingKeys: String, CodingKey {
        case interests = "interests"
    }
}

public struct UpdateUAUserProfileRequest: Codable {
    public let userId: String
    public let contentPreferences: UAUserContentPreferences

    public init(userId: String, contentPreferences: UAUserContentPreferences) {
        self.userId = userId
        self.contentPreferences = contentPreferences
    }

    enum CodingKeys: String, CodingKey {
        case userId = "user_id"
        case contentPreferences = "content_preferences"
    }
}
