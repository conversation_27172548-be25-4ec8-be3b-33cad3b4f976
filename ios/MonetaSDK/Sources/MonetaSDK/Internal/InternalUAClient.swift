import Foundation

internal class InternalUAClient {
    private let baseUrl: String
    private let networkClient: InternalNetworkClient
    private let signatureGenerator: SignatureGenerator
    
    init(baseUrl: String, networkClient: InternalNetworkClient, signatureGenerator: SignatureGenerator) {
        self.baseUrl = baseUrl
        self.networkClient = networkClient
        self.signatureGenerator = signatureGenerator
    }
    
    func getRecommendations(request: RecommendationsRequest) async throws -> ApiResponse<[AnyCodable]> {
        guard let url = URL(string: "\(baseUrl)/recommendations") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        
        var urlRequest = URLRequest(url: url)
        try signatureGenerator.addSignatureHeaders(to: &urlRequest)
        
        return try await networkClient.post(url: url, body: request)
    }
    
    func updateUserProfile(request: UpdateUAUserProfileRequest) async throws -> ApiResponse<Bool> {
        guard let url = URL(string: "\(baseUrl)/user-profile") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        
        var urlRequest = URLRequest(url: url)
        try signatureGenerator.addSignatureHeaders(to: &urlRequest)
        
        return try await networkClient.put(url: url, body: request)
    }
}
