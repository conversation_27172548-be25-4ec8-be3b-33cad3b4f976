import Foundation
import CryptoKit

internal class SignatureGenerator {
    private let secureStorage: SecureStorage
    
    init(secureStorage: SecureStorage) {
        self.secureStorage = secureStorage
    }
    
    func addSignatureHeaders(to request: inout URLRequest) throws {
        // Check if this request requires a signature
        guard requiresSignature(request) else {
            return
        }
        
        // Get onboarding data and private key
        guard let onboardingDataString = try secureStorage.getData("onboarding_data"),
              let privateKeyString = try secureStorage.getData("private_key") else {
            return // Skip signature if no onboarding data or private key
        }
        
        // Extract client ID from onboarding data
        // In a real implementation, parse the JSON onboarding data to get clientId
        let clientId = "example_client_id" // Replace with actual parsing
        
        // Generate timestamp
        let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))
        
        // Get request details
        let method = request.httpMethod ?? "GET"
        let uri = request.url?.absoluteString ?? ""
        let body = request.httpBody.map { String(data: $0, encoding: .utf8) ?? "" } ?? ""
        
        // Generate signature
        let signature = try generateSignature(
            method: method,
            uri: uri,
            clientId: clientId,
            timestamp: timestamp,
            body: body,
            privateKeyString: privateKeyString
        )
        
        // Add headers
        request.addValue(clientId, forHTTPHeaderField: "Client-ID")
        request.addValue("signature=\(signature), algorithm=RSA-SHA256", forHTTPHeaderField: "Signature")
        request.addValue(timestamp, forHTTPHeaderField: "Request-Time")
    }
    
    private func requiresSignature(_ request: URLRequest) -> Bool {
        // In a real implementation, check for the RequiresSignature annotation
        // This is a simplified placeholder
        return request.url?.absoluteString.contains("/api/mo/") ?? false
    }
    
    private func generateSignature(
        method: String,
        uri: String,
        clientId: String,
        timestamp: String,
        body: String,
        privateKeyString: String
    ) throws -> String {
        // Construct the canonical string
        let canonicalString = "\(method)\n\(uri)\n\(clientId)\n\(timestamp)\n\(body)"
        
        // Convert private key string to SecKey
        guard let privateKeyData = Data(base64Encoded: privateKeyString),
              let privateKey = try? SecKeyCreateWithData(privateKeyData as CFData, [:] as CFDictionary, nil) else {
            throw MonetaSDKError.cryptoError("Failed to create private key")
        }
        
        // Generate signature
        guard let canonicalData = canonicalString.data(using: .utf8) else {
            throw MonetaSDKError.cryptoError("Failed to convert canonical string to data")
        }
        
        var error: Unmanaged<CFError>?
        guard let signatureData = SecKeyCreateSignature(
            privateKey,
            .rsaSignatureMessagePKCS1v15SHA256,
            canonicalData as CFData,
            &error
        ) as Data? else {
            throw MonetaSDKError.cryptoError("Failed to generate signature: \(error?.takeRetainedValue().localizedDescription ?? "unknown error")")
        }
        
        // Return Base64 encoded signature
        return signatureData.base64EncodedString()
    }
}