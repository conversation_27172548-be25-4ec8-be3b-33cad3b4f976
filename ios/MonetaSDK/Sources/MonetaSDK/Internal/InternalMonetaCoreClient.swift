import Foundation

internal class InternalMonetaCoreClient {
    private let baseUrl: String
    private let networkClient: InternalNetworkClient
    private let signatureGenerator: SignatureGenerator
    
    init(baseUrl: String, networkClient: InternalNetworkClient, signatureGenerator: SignatureGenerator) {
        self.baseUrl = baseUrl
        self.networkClient = networkClient
        self.signatureGenerator = signatureGenerator
    }
    
    func getTransactions(page: Int, size: Int) async throws -> ApiResponse<PaginatedResponse<TransactionResponse>> {
        guard let url = URL(string: "\(baseUrl)/transactions?page=\(page)&size=\(size)") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        
        var urlRequest = URLRequest(url: url)
        try signatureGenerator.addSignatureHeaders(to: &urlRequest)
        
        return try await networkClient.get(url: url)
    }
    
    func getTransaction(transactionId: String) async throws -> ApiResponse<TransactionResponse> {
        guard let url = URL(string: "\(baseUrl)/transactions/\(transactionId)") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        
        var urlRequest = URLRequest(url: url)
        try signatureGenerator.addSignatureHeaders(to: &urlRequest)
        
        return try await networkClient.get(url: url)
    }
    
    func approveSubscriptionCharge(
        transactionId: String,
        request: ApprovalSubscriptionChargeRequest
    ) async throws -> ApiResponse<TransactionResponse> {
        guard let url = URL(string: "\(baseUrl)/transactions/\(transactionId)/approve") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        
        var urlRequest = URLRequest(url: url)
        try signatureGenerator.addSignatureHeaders(to: &urlRequest)
        
        return try await networkClient.put(url: url, body: request)
    }
    
    func createDispute(request: CreateDisputeRequest) async throws -> ApiResponse<Bool> {
        guard let url = URL(string: "\(baseUrl)/disputes") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        
        var urlRequest = URLRequest(url: url)
        try signatureGenerator.addSignatureHeaders(to: &urlRequest)
        
        return try await networkClient.post(url: url, body: request)
    }
}
