import Foundation

internal class InternalMembershipOrgClient {
    private let baseUrl: String
    private let networkClient: InternalNetworkClient
    private let signatureGenerator: SignatureGenerator
    
    init(baseUrl: String, networkClient: InternalNetworkClient, signatureGenerator: SignatureGenerator) {
        self.baseUrl = baseUrl
        self.networkClient = networkClient
        self.signatureGenerator = signatureGenerator
    }
    
    func startOnboarding(request: OnboardingRequest) async throws -> ApiResponse<OnboardVerificationResponse> {
        guard let url = URL(string: "\(baseUrl)/onboard/verify") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        
        var urlRequest = URLRequest(url: url)
        try signatureGenerator.addSignatureHeaders(to: &urlRequest)
        
        return try await networkClient.post(url: url, body: request)
    }
    
    func completeOnboarding() async throws -> ApiResponse<OnboardUserResponse> {
        guard let url = URL(string: "\(baseUrl)/onboard/user") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        
        var urlRequest = URLRequest(url: url)
        try signatureGenerator.addSignatureHeaders(to: &urlRequest)
        
        return try await networkClient.get(url: url)
    }
    
    func publisherLogin(session: String) async throws -> ApiResponse<OnboardUserResponse> {
        guard let url = URL(string: "\(baseUrl)/publisher/login") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        
        var urlRequest = URLRequest(url: url)
        try signatureGenerator.addSignatureHeaders(to: &urlRequest)
        
        let request = PublisherLoginRequest(session: session)
        return try await networkClient.post(url: url, body: request)
    }
}
