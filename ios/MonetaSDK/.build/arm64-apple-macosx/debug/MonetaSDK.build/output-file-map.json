{"": {"swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/master.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMembershipOrgClient.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalMembershipOrgClient.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalMembershipOrgClient.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalMembershipOrgClient~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalMembershipOrgClient.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMonetaCoreClient.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalMonetaCoreClient.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalMonetaCoreClient.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalMonetaCoreClient~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalMonetaCoreClient.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalNetworkClient.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalNetworkClient.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalNetworkClient.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalNetworkClient~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalNetworkClient.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalUAClient.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalUAClient.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalUAClient.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalUAClient~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InternalUAClient.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SecureStorage.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorage.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorage.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorage~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorage.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SignatureGenerator.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SignatureGenerator.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SignatureGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SignatureGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SignatureGenerator.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/RequestModels.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestModels.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestModels.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestModels~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestModels.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/ResponseModels.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseModels.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseModels.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseModels~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseModels.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/AnyCodable.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AnyCodable.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AnyCodable.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AnyCodable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AnyCodable.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaError.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaSDKError.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDKError.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDKError.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDKError~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDKError.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/SecKey+Extensions.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecKey+Extensions.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecKey+Extensions.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecKey+Extensions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecKey+Extensions.swiftdeps"}}