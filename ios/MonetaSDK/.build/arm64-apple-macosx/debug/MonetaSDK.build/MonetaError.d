/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError.swift.o : /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/MultipartFormData.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserFeed.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecureStorageService.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Balance.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineStorage.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Storage/SecureStorage.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/AnyCodable.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Article.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserProfile.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseMiddleware.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Response.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SigningURLSessionDelegate.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Dispute.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RequestQueue.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Billing.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClientImpl.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Authentication.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Validation.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/JSONSerialization.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Transaction.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Subscription.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestBuilder.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SyncManager.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SimpleSyncManager.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CacheManager.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/AuthenticationManager.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/CryptoManager.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/HeaderManager.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/InterceptorManager.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Logging/Logger.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/TransactionLogger.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/RequestSigner.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseWrapper.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/ConflictResolver.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/HTTPError.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/MonetaError.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/ValidationError.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestValidator.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecurityAuditor.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/NetworkMonitor.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/ResponseInterceptor.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingResponseInterceptor.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingResponseInterceptor.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/RequestInterceptor.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/OfflineRequestInterceptor.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingRequestInterceptor.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingRequestInterceptor.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/CommonResponses.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ModelProtocols.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/APIMarkerProtocols.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Recommendations.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClient.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/SDKEnvironment.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Request.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineRequest.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserPolicy.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RetryStrategy.swift /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ConsumptionActivity.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/XPC.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/Distributed.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/unistd.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/_time.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/sys_time.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/OSLog.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/Dispatch.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/_math.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Network.framework/Modules/Network.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/_signal.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/System.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/LocalAuthentication.framework/Modules/LocalAuthentication.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/Observation.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/_stdio.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/_errno.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/os.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/IOKit.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CryptoKit.framework/Modules/CryptoKit.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/SwiftOnoneSupport.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/XPC.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/CoreData.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/Distributed.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/unistd.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/_time.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/sys_time.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/Combine.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/OSLog.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/Dispatch.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/_math.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/Network.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/_signal.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/System.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/Darwin.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/LocalAuthentication.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/Foundation.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/Observation.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/_stdio.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/_errno.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/os.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/IOKit.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/CryptoKit.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/SwiftOnoneSupport.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/XPC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/ObjectiveC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_time.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/Dispatch.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Network.framework/Headers/Network.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/LocalAuthentication.framework/Headers/LocalAuthentication.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/os.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Security.framework/Headers/Security.apinotes
