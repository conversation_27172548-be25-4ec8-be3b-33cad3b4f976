{"": {"swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/master.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/runner.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/runner.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/runner~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/runner.swiftdeps"}}