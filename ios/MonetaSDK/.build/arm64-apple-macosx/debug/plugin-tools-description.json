{"builtTestProducts": [{"binaryPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.xctest/Contents/MacOS/MonetaSDKPackageTests", "packagePath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK", "productName": "MonetaSDKPackageTests"}], "copyCommands": {}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.MonetaSDK-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/sources", "importPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/APIMarkerProtocols.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/AnyCodable.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/SDKEnvironment.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/ConflictResolver.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Article.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Authentication.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Balance.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Billing.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/CommonResponses.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ConsumptionActivity.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Dispute.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ModelProtocols.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Recommendations.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Subscription.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Transaction.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserFeed.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserPolicy.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserProfile.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Validation.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineRequest.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineStorage.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RequestQueue.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RetryStrategy.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SimpleSyncManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Storage/SecureStorage.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SyncManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/TransactionLogger.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/HTTPError.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/MonetaError.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/ValidationError.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Logging/Logger.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClient.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClientImpl.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/HeaderManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CacheManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingRequestInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingResponseInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/InterceptorManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingRequestInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingResponseInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/OfflineRequestInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/RequestInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/ResponseInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/JSONSerialization.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/MultipartFormData.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/NetworkMonitor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Request.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestBuilder.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestValidator.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Response.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseMiddleware.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseWrapper.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/AuthenticationManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/CryptoManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/RequestSigner.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecureStorageService.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecurityAuditor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SigningURLSessionDelegate.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/sources"}], "isLibrary": true, "moduleName": "MonetaSDK", "moduleOutputPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule", "objects": ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIMarkerProtocols.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AnyCodable.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SDKEnvironment.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConflictResolver.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Article.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Authentication.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Balance.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Billing.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CommonResponses.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConsumptionActivity.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Dispute.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ModelProtocols.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Recommendations.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Subscription.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Transaction.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserFeed.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserPolicy.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserProfile.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Validation.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequest.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineStorage.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestQueue.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RetryStrategy.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SimpleSyncManager.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorage.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SyncManager.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/TransactionLogger.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HTTPError.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ValidationError.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Logger.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClient.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClientImpl.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HeaderManager.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CacheManager.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingRequestInterceptor.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingResponseInterceptor.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InterceptorManager.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingRequestInterceptor.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingResponseInterceptor.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequestInterceptor.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestInterceptor.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseInterceptor.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/JSONSerialization.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MultipartFormData.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/NetworkMonitor.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Request.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestBuilder.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestValidator.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Response.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseMiddleware.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseWrapper.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AuthenticationManager.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CryptoManager.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestSigner.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorageService.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecurityAuditor.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SigningURLSessionDelegate.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx12.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "monetasdk"], "outputFileMapPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIMarkerProtocols.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AnyCodable.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SDKEnvironment.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConflictResolver.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Article.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Authentication.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Balance.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Billing.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CommonResponses.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConsumptionActivity.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Dispute.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ModelProtocols.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Recommendations.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Subscription.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Transaction.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserFeed.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserPolicy.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserProfile.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Validation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequest.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineStorage.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestQueue.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RetryStrategy.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SimpleSyncManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorage.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SyncManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/TransactionLogger.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HTTPError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ValidationError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Logger.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClient.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClientImpl.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HeaderManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CacheManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingRequestInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingResponseInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InterceptorManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingRequestInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingResponseInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequestInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/JSONSerialization.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MultipartFormData.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/NetworkMonitor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Request.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestBuilder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestValidator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Response.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseMiddleware.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseWrapper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AuthenticationManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CryptoManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestSigner.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorageService.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecurityAuditor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SigningURLSessionDelegate.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/APIMarkerProtocols.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/AnyCodable.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/SDKEnvironment.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/ConflictResolver.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Article.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Authentication.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Balance.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Billing.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/CommonResponses.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ConsumptionActivity.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Dispute.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ModelProtocols.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Recommendations.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Subscription.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Transaction.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserFeed.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserPolicy.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserProfile.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Validation.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineRequest.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineStorage.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RequestQueue.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RetryStrategy.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SimpleSyncManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Storage/SecureStorage.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SyncManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/TransactionLogger.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/HTTPError.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/MonetaError.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/ValidationError.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Logging/Logger.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClient.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClientImpl.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/HeaderManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CacheManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingRequestInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingResponseInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/InterceptorManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingRequestInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingResponseInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/OfflineRequestInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/RequestInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/ResponseInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/JSONSerialization.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/MultipartFormData.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/NetworkMonitor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Request.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestBuilder.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestValidator.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Response.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseMiddleware.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseWrapper.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/AuthenticationManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/CryptoManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/RequestSigner.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecureStorageService.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecurityAuditor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SigningURLSessionDelegate.swift"], "tempsPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build", "wholeModuleOptimization": false}, "C.MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources", "importPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources"}], "isLibrary": true, "moduleName": "MonetaSDKPackageTests", "moduleOutputPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKPackageTests.swiftmodule", "objects": ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/runner.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx12.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/MonetaSDKPackageTests-Swift.h", "-color-diagnostics", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "monetasdk"], "outputFileMapPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/runner.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKPackageTests.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift"], "tempsPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build", "wholeModuleOptimization": false}, "C.MonetaSDKTests-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources", "importPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources"}], "isLibrary": true, "moduleName": "MonetaSDKTests", "moduleOutputPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule", "objects": ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleTests.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/NetworkErrorTests.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/StorageErrorTests.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleResponseTests.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/AuthenticationTests.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CommonResponsesTests.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MonetaSDKTests.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/APIClientTests.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CachingTests.swift.o", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MockCacheClasses.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx14.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "monetasdk"], "outputFileMapPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/NetworkErrorTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/StorageErrorTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleResponseTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/AuthenticationTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CommonResponsesTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MonetaSDKTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/APIClientTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CachingTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MockCacheClasses.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift"], "tempsPath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"MonetaSDK": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "MonetaSDK", "-package-name", "monetasdk", "-incremental", "-c", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/APIMarkerProtocols.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/AnyCodable.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/SDKEnvironment.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/ConflictResolver.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Article.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Authentication.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Balance.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Billing.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/CommonResponses.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ConsumptionActivity.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Dispute.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ModelProtocols.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Recommendations.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Subscription.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Transaction.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserFeed.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserPolicy.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserProfile.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Validation.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineRequest.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineStorage.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RequestQueue.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RetryStrategy.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SimpleSyncManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Storage/SecureStorage.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SyncManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/TransactionLogger.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/HTTPError.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/MonetaError.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/ValidationError.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Logging/Logger.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClient.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClientImpl.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/HeaderManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CacheManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingRequestInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingResponseInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/InterceptorManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingRequestInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingResponseInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/OfflineRequestInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/RequestInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/ResponseInterceptor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/JSONSerialization.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/MultipartFormData.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/NetworkMonitor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Request.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestBuilder.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestValidator.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Response.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseMiddleware.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseWrapper.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/AuthenticationManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/CryptoManager.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/RequestSigner.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecureStorageService.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecurityAuditor.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SigningURLSessionDelegate.swift", "-I", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx12.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "monetasdk", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "MonetaSDKPackageTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "MonetaSDKPackageTests", "-package-name", "monetasdk", "-incremental", "-c", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift", "-I", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx12.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/MonetaSDKPackageTests-Swift.h", "-color-diagnostics", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "monetasdk", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "MonetaSDKTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "MonetaSDKTests", "-package-name", "monetasdk", "-incremental", "-c", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift", "-I", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx14.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "monetasdk", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"MonetaSDK": [], "MonetaSDKPackageTests": ["MonetaSDKTests", "MonetaSDK"], "MonetaSDKTests": ["MonetaSDK"]}, "testDiscoveryCommands": {}, "testEntryPointCommands": {"/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift": {"inputs": [], "outputs": [{"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift"}]}}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/APIMarkerProtocols.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/AnyCodable.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/SDKEnvironment.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/ConflictResolver.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Article.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Authentication.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Balance.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Billing.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/CommonResponses.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ConsumptionActivity.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Dispute.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ModelProtocols.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Recommendations.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Subscription.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Transaction.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserFeed.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserPolicy.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserProfile.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Validation.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineRequest.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineStorage.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RequestQueue.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RetryStrategy.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SimpleSyncManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Storage/SecureStorage.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SyncManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/TransactionLogger.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/HTTPError.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/MonetaError.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/ValidationError.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Logging/Logger.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClient.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClientImpl.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/HeaderManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CacheManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingRequestInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingResponseInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/InterceptorManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingRequestInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingResponseInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/OfflineRequestInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/RequestInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/ResponseInterceptor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/JSONSerialization.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/MultipartFormData.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/NetworkMonitor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Request.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestBuilder.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestValidator.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Response.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseMiddleware.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseWrapper.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/AuthenticationManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/CryptoManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/RequestSigner.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecureStorageService.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecurityAuditor.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SigningURLSessionDelegate.swift"}], "outputFilePath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/sources"}, "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift"}], "outputFilePath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources"}, "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClient.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClientImpl.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIMarkerProtocols.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AnyCodable.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Article.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Authentication.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AuthenticationManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Balance.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Billing.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CacheManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingRequestInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingResponseInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CommonResponses.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConflictResolver.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConsumptionActivity.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CryptoManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Dispute.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HTTPError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HeaderManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InterceptorManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/JSONSerialization.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Logger.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingRequestInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingResponseInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ModelProtocols.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MultipartFormData.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/NetworkMonitor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequest.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequestInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineStorage.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Recommendations.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Request.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestBuilder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestQueue.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestSigner.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestValidator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Response.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseInterceptor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseMiddleware.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseWrapper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RetryStrategy.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SDKEnvironment.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorage.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorageService.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecurityAuditor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SigningURLSessionDelegate.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SimpleSyncManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Subscription.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SyncManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Transaction.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/TransactionLogger.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserFeed.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserPolicy.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserProfile.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Validation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ValidationError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/runner.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/APIClientTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleResponseTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/AuthenticationTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CachingTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CommonResponsesTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MockCacheClasses.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MonetaSDKTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/NetworkErrorTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/StorageErrorTests.swift.o"}], "outputFilePath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.product/Objects.LinkFileList"}, "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift"}], "outputFilePath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources"}, "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}}}