client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "MonetaSDK-arm64-apple-macosx15.0-debug.module": ["<MonetaSDK-arm64-apple-macosx15.0-debug.module>"]
  "MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.module": ["<MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.module>"]
  "MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.test": ["<MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.test>"]
  "MonetaSDKTests-arm64-apple-macosx15.0-debug.module": ["<MonetaSDKTests-arm64-apple-macosx15.0-debug.module>"]
  "PackageStructure": ["<PackageStructure>"]
  "main": ["<MonetaSDK-arm64-apple-macosx15.0-debug.module>"]
  "test": ["<MonetaSDK-arm64-apple-macosx15.0-debug.module>","<MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.test>","<MonetaSDKTests-arm64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
commands:
  "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/APIMarkerProtocols.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/AnyCodable.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/SDKEnvironment.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/ConflictResolver.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Article.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Authentication.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Balance.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Billing.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/CommonResponses.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ConsumptionActivity.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Dispute.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ModelProtocols.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Recommendations.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Subscription.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Transaction.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserFeed.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserPolicy.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserProfile.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Validation.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineRequest.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineStorage.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RequestQueue.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RetryStrategy.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SimpleSyncManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Storage/SecureStorage.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SyncManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/TransactionLogger.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/HTTPError.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/MonetaError.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/ValidationError.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Logging/Logger.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClient.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClientImpl.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/HeaderManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CacheManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingRequestInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingResponseInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/InterceptorManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingRequestInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingResponseInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/OfflineRequestInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/RequestInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/ResponseInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/JSONSerialization.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/MultipartFormData.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/NetworkMonitor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Request.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestBuilder.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestValidator.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Response.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseMiddleware.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseWrapper.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/AuthenticationManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/CryptoManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/RequestSigner.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecureStorageService.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecurityAuditor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SigningURLSessionDelegate.swift"]
    outputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/sources"

  "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift"]
    outputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources"

  "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift":
    tool: test-entry-point-tool
    inputs: []
    outputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift"]

  "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClient.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClientImpl.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIMarkerProtocols.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AnyCodable.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Article.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Authentication.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AuthenticationManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Balance.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Billing.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CacheManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingRequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingResponseInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CommonResponses.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConflictResolver.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConsumptionActivity.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CryptoManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Dispute.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HTTPError.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HeaderManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InterceptorManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/JSONSerialization.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Logger.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingRequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingResponseInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ModelProtocols.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MultipartFormData.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/NetworkMonitor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequest.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineStorage.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Recommendations.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Request.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestBuilder.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestQueue.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestSigner.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestValidator.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Response.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseMiddleware.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseWrapper.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RetryStrategy.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SDKEnvironment.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorage.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorageService.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecurityAuditor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SigningURLSessionDelegate.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SimpleSyncManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Subscription.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SyncManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Transaction.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/TransactionLogger.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserFeed.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserPolicy.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserProfile.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Validation.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ValidationError.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/runner.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/APIClientTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleResponseTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/AuthenticationTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CachingTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CommonResponsesTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MockCacheClasses.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MonetaSDKTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/NetworkErrorTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/StorageErrorTests.swift.o"]
    outputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.product/Objects.LinkFileList"

  "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift"]
    outputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources"

  "/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"

  "<MonetaSDK-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIMarkerProtocols.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AnyCodable.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SDKEnvironment.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConflictResolver.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Article.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Authentication.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Balance.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Billing.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CommonResponses.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConsumptionActivity.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Dispute.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ModelProtocols.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Recommendations.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Subscription.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Transaction.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserFeed.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserPolicy.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserProfile.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Validation.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequest.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineStorage.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestQueue.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RetryStrategy.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SimpleSyncManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorage.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SyncManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/TransactionLogger.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HTTPError.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ValidationError.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Logger.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClient.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClientImpl.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HeaderManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CacheManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingRequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingResponseInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InterceptorManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingRequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingResponseInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/JSONSerialization.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MultipartFormData.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/NetworkMonitor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Request.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestBuilder.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestValidator.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Response.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseMiddleware.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseWrapper.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AuthenticationManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CryptoManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestSigner.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorageService.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecurityAuditor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SigningURLSessionDelegate.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule"]
    outputs: ["<MonetaSDK-arm64-apple-macosx15.0-debug.module>"]

  "<MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/runner.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKPackageTests.swiftmodule"]
    outputs: ["<MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.module>"]

  "<MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.test>":
    tool: phony
    inputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.xctest/Contents/MacOS/MonetaSDKPackageTests"]
    outputs: ["<MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.test>"]

  "<MonetaSDKTests-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/NetworkErrorTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/StorageErrorTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleResponseTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/AuthenticationTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CommonResponsesTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MonetaSDKTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/APIClientTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CachingTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MockCacheClasses.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule"]
    outputs: ["<MonetaSDKTests-arm64-apple-macosx15.0-debug.module>"]

  "C.MonetaSDK-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/APIMarkerProtocols.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/AnyCodable.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Core/SDKEnvironment.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/ConflictResolver.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Article.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Authentication.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Balance.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Billing.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/CommonResponses.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ConsumptionActivity.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Dispute.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/ModelProtocols.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Recommendations.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Subscription.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Transaction.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserFeed.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserPolicy.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/UserProfile.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Models/Validation.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineRequest.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/OfflineStorage.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RequestQueue.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/RetryStrategy.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SimpleSyncManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/Storage/SecureStorage.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/SyncManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Data/TransactionLogger.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/HTTPError.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/MonetaError.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Errors/ValidationError.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Logging/Logger.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClient.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/APIClientImpl.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/HeaderManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CacheManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingRequestInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/CachingResponseInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/InterceptorManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingRequestInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/LoggingResponseInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/OfflineRequestInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/RequestInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Interceptor/ResponseInterceptor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/JSONSerialization.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/MultipartFormData.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/NetworkMonitor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Request.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestBuilder.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/RequestValidator.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/Response.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseMiddleware.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Network/ResponseWrapper.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/AuthenticationManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/CryptoManager.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/RequestSigner.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecureStorageService.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SecurityAuditor.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/Security/SigningURLSessionDelegate.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/sources"]
    outputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIMarkerProtocols.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AnyCodable.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SDKEnvironment.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConflictResolver.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Article.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Authentication.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Balance.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Billing.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CommonResponses.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConsumptionActivity.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Dispute.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ModelProtocols.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Recommendations.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Subscription.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Transaction.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserFeed.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserPolicy.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserProfile.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Validation.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequest.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineStorage.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestQueue.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RetryStrategy.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SimpleSyncManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorage.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SyncManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/TransactionLogger.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HTTPError.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ValidationError.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Logger.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClient.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClientImpl.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HeaderManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CacheManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingRequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingResponseInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InterceptorManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingRequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingResponseInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/JSONSerialization.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MultipartFormData.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/NetworkMonitor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Request.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestBuilder.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestValidator.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Response.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseMiddleware.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseWrapper.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AuthenticationManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CryptoManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestSigner.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorageService.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecurityAuditor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SigningURLSessionDelegate.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule"]
    description: "Compiling Swift Module 'MonetaSDK' (59 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","MonetaSDK","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule","-output-file-map","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/sources","-I","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx12.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK-Swift.h","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","monetasdk"]

  "C.MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources"]
    outputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/runner.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKPackageTests.swiftmodule"]
    description: "Compiling Swift Module 'MonetaSDKPackageTests' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","MonetaSDKPackageTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKPackageTests.swiftmodule","-output-file-map","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources","-I","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx12.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-Xfrontend","-enable-cross-import-overlays","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/MonetaSDKPackageTests-Swift.h","-color-diagnostics","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","monetasdk"]

  "C.MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.test":
    tool: shell
    inputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClient.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIClientImpl.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/APIMarkerProtocols.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AnyCodable.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Article.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Authentication.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/AuthenticationManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Balance.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Billing.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CacheManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingRequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CachingResponseInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CommonResponses.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConflictResolver.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ConsumptionActivity.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/CryptoManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Dispute.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HTTPError.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/HeaderManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/InterceptorManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/JSONSerialization.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Logger.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingRequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/LoggingResponseInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ModelProtocols.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/MultipartFormData.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/NetworkMonitor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequest.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineRequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/OfflineStorage.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Recommendations.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Request.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestBuilder.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestQueue.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestSigner.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RequestValidator.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Response.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseInterceptor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseMiddleware.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseWrapper.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/RetryStrategy.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SDKEnvironment.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorage.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorageService.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SecurityAuditor.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SigningURLSessionDelegate.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SimpleSyncManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Subscription.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/SyncManager.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Transaction.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/TransactionLogger.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserFeed.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserPolicy.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/UserProfile.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/Validation.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDK.build/ValidationError.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/runner.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/APIClientTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleResponseTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/AuthenticationTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CachingTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CommonResponsesTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MockCacheClasses.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MonetaSDKTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/NetworkErrorTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/StorageErrorTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.xctest/Contents/MacOS/MonetaSDKPackageTests"]
    description: "Linking ./.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.xctest/Contents/MacOS/MonetaSDKPackageTests"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug","-o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.xctest/Contents/MacOS/MonetaSDKPackageTests","-module-name","MonetaSDKPackageTests","-Xlinker","-no_warn_duplicate_libraries","-Xlinker","-bundle","-Xlinker","-rpath","-Xlinker","@loader_path/../../../","@/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKPackageTests.product/Objects.LinkFileList","-target","arm64-apple-macosx14.0","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKPackageTests.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.MonetaSDKTests-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources"]
    outputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/NetworkErrorTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/StorageErrorTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleResponseTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/AuthenticationTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CommonResponsesTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MonetaSDKTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/APIClientTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/CachingTests.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/MockCacheClasses.swift.o","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule"]
    description: "Compiling Swift Module 'MonetaSDKTests' (10 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","MonetaSDKTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule","-output-file-map","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources","-I","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx14.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-Xfrontend","-enable-cross-import-overlays","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","monetasdk"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Sources/MonetaSDK/","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Tests/MonetaSDKTests/","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Package.swift","/Users/<USER>/Workspace/CoderPush/moneta-mobile-sdks/moneta-ios-sdk/MonetaSDK/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

