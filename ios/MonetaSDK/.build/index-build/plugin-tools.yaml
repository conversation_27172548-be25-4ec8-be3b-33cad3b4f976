client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "MonetaSDK-arm64-apple-macosx15.0-debug.module": ["<MonetaSDK-arm64-apple-macosx15.0-debug.module>"]
  "MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.module": ["<MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.module>"]
  "MonetaSDKTests-arm64-apple-macosx15.0-debug.module": ["<MonetaSDKTests-arm64-apple-macosx15.0-debug.module>"]
  "PackageStructure": ["<PackageStructure>"]
  "main": ["<MonetaSDK-arm64-apple-macosx15.0-debug.module>"]
  "test": ["<MonetaSDK-arm64-apple-macosx15.0-debug.module>","<MonetaSDKTests-arm64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
commands:
  "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMembershipOrgClient.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMonetaCoreClient.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalNetworkClient.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalUAClient.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SecureStorage.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SignatureGenerator.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/RequestModels.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/ResponseModels.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/AnyCodable.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaError.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaSDKError.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/SecKey+Extensions.swift"]
    outputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/sources"

  "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift"]
    outputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources"

  "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift"]
    outputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources"

  "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"

  "<MonetaSDK-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule"]
    outputs: ["<MonetaSDK-arm64-apple-macosx15.0-debug.module>"]

  "<MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDKPackageTests.swiftmodule"]
    outputs: ["<MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.module>"]

  "<MonetaSDKTests-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule"]
    outputs: ["<MonetaSDKTests-arm64-apple-macosx15.0-debug.module>"]

  "C.MonetaSDK-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMembershipOrgClient.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMonetaCoreClient.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalNetworkClient.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalUAClient.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SecureStorage.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SignatureGenerator.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/RequestModels.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/ResponseModels.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/AnyCodable.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaError.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaSDKError.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/SecKey+Extensions.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/sources"]
    outputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule"]
    description: "Compiling Swift Module 'MonetaSDK' (13 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","MonetaSDK","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule","-output-file-map","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/output-file-map.json","-parse-as-library","-incremental","@/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/sources","-I","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx12.0","-enable-batch-mode","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK-Swift.h","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","monetasdk"]

  "C.MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources"]
    outputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDKPackageTests.swiftmodule"]
    description: "Compiling Swift Module 'MonetaSDKPackageTests' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","MonetaSDKPackageTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDKPackageTests.swiftmodule","-output-file-map","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/output-file-map.json","-parse-as-library","-incremental","@/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources","-I","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx12.0","-enable-batch-mode","-Onone","-enable-testing","-Xfrontend","-enable-cross-import-overlays","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/MonetaSDKPackageTests-Swift.h","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","monetasdk"]

  "C.MonetaSDKTests-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources"]
    outputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule"]
    description: "Compiling Swift Module 'MonetaSDKTests' (10 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","MonetaSDKTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule","-output-file-map","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/output-file-map.json","-parse-as-library","-incremental","@/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources","-I","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx14.0","-enable-batch-mode","-Onone","-enable-testing","-Xfrontend","-enable-cross-import-overlays","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","monetasdk"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Package.swift","/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

