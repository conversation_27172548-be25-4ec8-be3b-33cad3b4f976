{"": {"swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/master.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleTests.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleTests.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleTests.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/NetworkErrorTests.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/NetworkErrorTests.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/NetworkErrorTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/NetworkErrorTests.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/StorageErrorTests.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/StorageErrorTests.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/StorageErrorTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/StorageErrorTests.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleResponseTests.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleResponseTests.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleResponseTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleResponseTests.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/AuthenticationTests.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/AuthenticationTests.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/AuthenticationTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/AuthenticationTests.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/CommonResponsesTests.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/CommonResponsesTests.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/CommonResponsesTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/CommonResponsesTests.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/MonetaSDKTests.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/MonetaSDKTests.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/MonetaSDKTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/MonetaSDKTests.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/APIClientTests.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/APIClientTests.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/APIClientTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/APIClientTests.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/CachingTests.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/CachingTests.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/CachingTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/CachingTests.swiftdeps"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift": {"dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/MockCacheClasses.d", "object": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/MockCacheClasses.swift.o", "swiftmodule": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/MockCacheClasses~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/MockCacheClasses.swiftdeps"}}