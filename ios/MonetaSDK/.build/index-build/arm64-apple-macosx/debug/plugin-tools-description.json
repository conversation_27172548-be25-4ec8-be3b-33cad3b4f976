{"builtTestProducts": [{"binaryPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.xctest/Contents/MacOS/MonetaSDKPackageTests", "packagePath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK", "productName": "MonetaSDKPackageTests"}], "copyCommands": {}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.MonetaSDK-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/sources", "importPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMembershipOrgClient.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMonetaCoreClient.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalNetworkClient.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalUAClient.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SecureStorage.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SignatureGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/RequestModels.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/ResponseModels.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/AnyCodable.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaError.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaSDKError.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/SecKey+Extensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/sources"}], "isLibrary": true, "moduleName": "MonetaSDK", "moduleOutputPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule", "objects": ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/InternalMembershipOrgClient.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/InternalMonetaCoreClient.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/InternalNetworkClient.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/InternalUAClient.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/SecureStorage.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/SignatureGenerator.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/RequestModels.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/ResponseModels.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/AnyCodable.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaError.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDKError.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/SecKey+Extensions.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx12.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "monetasdk"], "outputFileMapPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMembershipOrgClient.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMonetaCoreClient.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalNetworkClient.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalUAClient.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SecureStorage.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SignatureGenerator.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/RequestModels.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/ResponseModels.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/AnyCodable.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaError.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaSDKError.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/SecKey+Extensions.swift"], "tempsPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build", "wholeModuleOptimization": false}, "C.MonetaSDKPackageTests-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources", "importPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources"}], "isLibrary": true, "moduleName": "MonetaSDKPackageTests", "moduleOutputPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDKPackageTests.swiftmodule", "objects": ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/runner.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx12.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/MonetaSDKPackageTests-Swift.h", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "monetasdk"], "outputFileMapPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDKPackageTests.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift"], "tempsPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build", "wholeModuleOptimization": false}, "C.MonetaSDKTests-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources", "importPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDK.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources"}], "isLibrary": true, "moduleName": "MonetaSDKTests", "moduleOutputPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule", "objects": ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleTests.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/NetworkErrorTests.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/StorageErrorTests.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/ArticleResponseTests.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/AuthenticationTests.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/CommonResponsesTests.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/MonetaSDKTests.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/APIClientTests.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/CachingTests.swift.o", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/MockCacheClasses.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx14.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "monetasdk"], "outputFileMapPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules/MonetaSDKTests.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift"], "tempsPath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"MonetaSDK": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "MonetaSDK", "-package-name", "monetasdk", "-incremental", "-c", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMembershipOrgClient.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMonetaCoreClient.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalNetworkClient.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalUAClient.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SecureStorage.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SignatureGenerator.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/RequestModels.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/ResponseModels.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/AnyCodable.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaError.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaSDKError.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/SecKey+Extensions.swift", "-I", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx12.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/MonetaSDK-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "monetasdk", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "MonetaSDKPackageTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "MonetaSDKPackageTests", "-package-name", "monetasdk", "-incremental", "-c", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift", "-I", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx12.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/MonetaSDKPackageTests-Swift.h", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "monetasdk", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "MonetaSDKTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "MonetaSDKTests", "-package-name", "monetasdk", "-incremental", "-c", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift", "-I", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx14.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "monetasdk", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"MonetaSDK": [], "MonetaSDKPackageTests": ["MonetaSDKTests", "MonetaSDK"], "MonetaSDKTests": ["MonetaSDK"]}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMembershipOrgClient.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalMonetaCoreClient.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalNetworkClient.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/InternalUAClient.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SecureStorage.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Internal/SignatureGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/RequestModels.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Models/ResponseModels.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/MonetaSDK.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/AnyCodable.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaError.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/MonetaSDKError.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Sources/MonetaSDK/Utils/SecKey+Extensions.swift"}], "outputFilePath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDK.build/sources"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.derived/runner.swift"}], "outputFilePath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKPackageTests.build/sources"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/ArticleTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/NetworkErrorTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Errors/StorageErrorTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/ArticleResponseTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/AuthenticationTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Models/CommonResponsesTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/MonetaSDKTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/APIClientTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/CachingTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/Tests/MonetaSDKTests/Network/MockCacheClasses.swift"}], "outputFilePath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/MonetaSDKTests.build/sources"}, "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/ios/MonetaSDK/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}}}